<?php
/**
 * QR Code Generator Class
 * Simple QR code generation using Google Charts API as fallback
 */

class QRCodeGenerator {
    private $db;
    private $upload_dir;
    
    public function __construct() {
        $this->db = Database::getInstance();
        $this->upload_dir = UPLOAD_DIR . 'qr_codes/';
        
        // Ensure upload directory exists
        if (!is_dir($this->upload_dir)) {
            mkdir($this->upload_dir, 0755, true);
        }
    }
    
    /**
     * Generate QR code image using cURL for better error handling
     */
    public function generateQRCode($data, $filename = null, $size = 300) {
        if (!$filename) {
            $filename = 'qr_' . time() . '_' . rand(1000, 9999) . '.png';
        }

        $filepath = $this->upload_dir . $filename;

        // Use Google Charts API for QR generation with cURL for better error handling
        $qr_url = "https://chart.googleapis.com/chart?chs={$size}x{$size}&cht=qr&chl=" . urlencode($data) . "&choe=UTF-8";

        // Use cURL instead of file_get_contents for better error handling
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $qr_url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);

        $qr_data = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);

        if ($qr_data !== false && $http_code === 200 && empty($error)) {
            file_put_contents($filepath, $qr_data);
            return $filename;
        }

        // If external API fails, create a simple text-based QR placeholder
        return $this->createPlaceholderQR($data, $filename);
    }

    /**
     * Create a simple placeholder QR code using an alternative method
     */
    private function createPlaceholderQR($data, $filename) {
        // Try a different QR API as fallback
        $qr_apis = [
            "https://api.qrserver.com/v1/create-qr-code/?size=300x300&data=" . urlencode($data),
            "https://chart.googleapis.com/chart?chs=300x300&cht=qr&chl=" . urlencode($data) . "&choe=UTF-8"
        ];

        foreach ($qr_apis as $api_url) {
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $api_url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 15);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');

            $qr_data = curl_exec($ch);
            $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $error = curl_error($ch);
            curl_close($ch);

            if ($qr_data !== false && $http_code === 200 && empty($error)) {
                $filepath = $this->upload_dir . $filename;
                file_put_contents($filepath, $qr_data);
                return $filename;
            }
        }

        // If all APIs fail, create a simple text file as last resort
        return $this->createTextPlaceholder($data, $filename);
    }

    /**
     * Create a text-based placeholder when image generation fails
     */
    private function createTextPlaceholder($data, $filename) {
        $filepath = $this->upload_dir . str_replace('.png', '.txt', $filename);

        $content = "QR CODE PLACEHOLDER\n";
        $content .= "==================\n\n";
        $content .= "Target URL: " . $data . "\n\n";
        $content .= "This is a text placeholder because QR code image generation failed.\n";
        $content .= "You can manually create a QR code for the above URL using any online QR generator.\n";

        file_put_contents($filepath, $content);
        return str_replace('.png', '.txt', $filename);
    }
    
    /**
     * Generate QR code for database record
     */
    public function generateForQRCode($qr_code_id) {
        try {
            // Get QR code data
            $stmt = $this->db->query("SELECT * FROM qr_codes WHERE id = ?", [$qr_code_id]);
            $qr_code = $stmt->fetch();
            
            if (!$qr_code) {
                throw new Exception("QR code not found");
            }
            
            // Generate filename
            $filename = 'qr_' . $qr_code['short_code'] . '_' . time() . '.png';
            
            // Generate QR code image
            $generated_filename = $this->generateQRCode($qr_code['short_url'], $filename);
            
            if ($generated_filename) {
                // Update database with image path
                $image_path = $this->upload_dir . $generated_filename;
                $sql = "UPDATE qr_codes SET qr_image_path = ? WHERE id = ?";
                $this->db->query($sql, [$image_path, $qr_code_id]);
                
                return $generated_filename;
            }
            
            return false;
            
        } catch (Exception $e) {
            error_log("QR Code generation error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get QR code image path
     */
    public function getQRCodePath($qr_code_id) {
        $stmt = $this->db->query("SELECT qr_image_path FROM qr_codes WHERE id = ?", [$qr_code_id]);
        $result = $stmt->fetch();
        
        return $result ? $result['qr_image_path'] : null;
    }
    
    /**
     * Download QR code
     */
    public function downloadQRCode($qr_code_id) {
        try {
            // Get QR code data
            $stmt = $this->db->query("SELECT * FROM qr_codes WHERE id = ?", [$qr_code_id]);
            $qr_code = $stmt->fetch();
            
            if (!$qr_code || !$qr_code['target_url']) {
                throw new Exception("QR code not found or not mapped to URL");
            }
            
            // Generate QR code if not exists
            if (!$qr_code['qr_image_path'] || !file_exists($qr_code['qr_image_path'])) {
                $this->generateForQRCode($qr_code_id);
                
                // Refresh data
                $stmt = $this->db->query("SELECT * FROM qr_codes WHERE id = ?", [$qr_code_id]);
                $qr_code = $stmt->fetch();
            }
            
            if ($qr_code['qr_image_path'] && file_exists($qr_code['qr_image_path'])) {
                // Update download count
                $sql = "UPDATE qr_codes SET download_count = download_count + 1 WHERE id = ?";
                $this->db->query($sql, [$qr_code_id]);

                // Set headers for download
                $safe_title = preg_replace('/[^a-zA-Z0-9_-]/', '_', $qr_code['title']);
                $file_extension = pathinfo($qr_code['qr_image_path'], PATHINFO_EXTENSION);
                $filename = 'QR_' . $qr_code['short_code'] . '_' . $safe_title . '.' . $file_extension;

                // Set appropriate content type
                if ($file_extension === 'txt') {
                    header('Content-Type: text/plain');
                } else {
                    header('Content-Type: image/png');
                }

                header('Content-Disposition: attachment; filename="' . $filename . '"');
                header('Content-Length: ' . filesize($qr_code['qr_image_path']));

                readfile($qr_code['qr_image_path']);
                exit;
            }
            
            throw new Exception("QR code image not found");
            
        } catch (Exception $e) {
            error_log("QR Code download error: " . $e->getMessage());

            // Show user-friendly error
            header('Content-Type: text/html');
            echo "<h2>QR Code Download Error</h2>";
            echo "<p>Sorry, there was an error downloading the QR code: " . htmlspecialchars($e->getMessage()) . "</p>";
            echo "<p><a href='javascript:history.back()'>Go Back</a></p>";
            exit;
        }
    }
    
    /**
     * Track QR code scan
     */
    public function trackScan($short_code, $ip_address = null, $user_agent = null, $referer = null) {
        try {
            // Get QR code
            $stmt = $this->db->query("SELECT id FROM qr_codes WHERE short_code = ? AND is_active = 1", [$short_code]);
            $qr_code = $stmt->fetch();
            
            if (!$qr_code) {
                return false;
            }
            
            // Insert scan record
            $sql = "INSERT INTO qr_scans (qr_code_id, ip_address, user_agent, referer) VALUES (?, ?, ?, ?)";
            $this->db->query($sql, [
                $qr_code['id'],
                $ip_address ?: $_SERVER['REMOTE_ADDR'] ?? null,
                $user_agent ?: $_SERVER['HTTP_USER_AGENT'] ?? null,
                $referer ?: $_SERVER['HTTP_REFERER'] ?? null
            ]);
            
            // Update scan count
            $sql = "UPDATE qr_codes SET scan_count = scan_count + 1 WHERE id = ?";
            $this->db->query($sql, [$qr_code['id']]);
            
            return true;
            
        } catch (Exception $e) {
            error_log("QR Code scan tracking error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get QR code by short code
     */
    public function getQRCodeByShortCode($short_code) {
        $stmt = $this->db->query("SELECT * FROM qr_codes WHERE short_code = ? AND is_active = 1", [$short_code]);
        return $stmt->fetch();
    }
}

/**
 * Helper function to sanitize filename
 */
function sanitizeFilename($filename) {
    // Remove special characters and spaces
    $filename = preg_replace('/[^a-zA-Z0-9\-_]/', '_', $filename);
    // Remove multiple underscores
    $filename = preg_replace('/_+/', '_', $filename);
    // Trim underscores from start and end
    return trim($filename, '_');
}
?>
