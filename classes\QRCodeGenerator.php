<?php
/**
 * QR Code Generator Class
 * Simple QR code generation using Google Charts API as fallback
 */

class QRCodeGenerator {
    private $db;
    private $upload_dir;
    
    public function __construct() {
        $this->db = Database::getInstance();
        $this->upload_dir = UPLOAD_DIR . 'qr_codes/';
        
        // Ensure upload directory exists
        if (!is_dir($this->upload_dir)) {
            mkdir($this->upload_dir, 0755, true);
        }
    }
    
    /**
     * Generate QR code image
     */
    public function generateQRCode($data, $filename = null, $size = 300) {
        if (!$filename) {
            $filename = 'qr_' . time() . '_' . rand(1000, 9999) . '.png';
        }
        
        $filepath = $this->upload_dir . $filename;
        
        // Use Google Charts API for QR generation (simple and reliable)
        $qr_url = "https://chart.googleapis.com/chart?chs={$size}x{$size}&cht=qr&chl=" . urlencode($data) . "&choe=UTF-8";
        
        // Download and save the QR code
        $qr_data = file_get_contents($qr_url);
        
        if ($qr_data !== false) {
            file_put_contents($filepath, $qr_data);
            return $filename;
        }
        
        return false;
    }
    
    /**
     * Generate QR code for database record
     */
    public function generateForQRCode($qr_code_id) {
        try {
            // Get QR code data
            $stmt = $this->db->query("SELECT * FROM qr_codes WHERE id = ?", [$qr_code_id]);
            $qr_code = $stmt->fetch();
            
            if (!$qr_code) {
                throw new Exception("QR code not found");
            }
            
            // Generate filename
            $filename = 'qr_' . $qr_code['short_code'] . '_' . time() . '.png';
            
            // Generate QR code image
            $generated_filename = $this->generateQRCode($qr_code['short_url'], $filename);
            
            if ($generated_filename) {
                // Update database with image path
                $image_path = $this->upload_dir . $generated_filename;
                $sql = "UPDATE qr_codes SET qr_image_path = ? WHERE id = ?";
                $this->db->query($sql, [$image_path, $qr_code_id]);
                
                return $generated_filename;
            }
            
            return false;
            
        } catch (Exception $e) {
            error_log("QR Code generation error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get QR code image path
     */
    public function getQRCodePath($qr_code_id) {
        $stmt = $this->db->query("SELECT qr_image_path FROM qr_codes WHERE id = ?", [$qr_code_id]);
        $result = $stmt->fetch();
        
        return $result ? $result['qr_image_path'] : null;
    }
    
    /**
     * Download QR code
     */
    public function downloadQRCode($qr_code_id) {
        try {
            // Get QR code data
            $stmt = $this->db->query("SELECT * FROM qr_codes WHERE id = ?", [$qr_code_id]);
            $qr_code = $stmt->fetch();
            
            if (!$qr_code || !$qr_code['target_url']) {
                throw new Exception("QR code not found or not mapped to URL");
            }
            
            // Generate QR code if not exists
            if (!$qr_code['qr_image_path'] || !file_exists($qr_code['qr_image_path'])) {
                $this->generateForQRCode($qr_code_id);
                
                // Refresh data
                $stmt = $this->db->query("SELECT * FROM qr_codes WHERE id = ?", [$qr_code_id]);
                $qr_code = $stmt->fetch();
            }
            
            if ($qr_code['qr_image_path'] && file_exists($qr_code['qr_image_path'])) {
                // Update download count
                $sql = "UPDATE qr_codes SET download_count = download_count + 1 WHERE id = ?";
                $this->db->query($sql, [$qr_code_id]);
                
                // Set headers for download
                $filename = 'QR_' . $qr_code['short_code'] . '_' . sanitizeFilename($qr_code['title']) . '.png';
                
                header('Content-Type: image/png');
                header('Content-Disposition: attachment; filename="' . $filename . '"');
                header('Content-Length: ' . filesize($qr_code['qr_image_path']));
                
                readfile($qr_code['qr_image_path']);
                exit;
            }
            
            throw new Exception("QR code image not found");
            
        } catch (Exception $e) {
            error_log("QR Code download error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Track QR code scan
     */
    public function trackScan($short_code, $ip_address = null, $user_agent = null, $referer = null) {
        try {
            // Get QR code
            $stmt = $this->db->query("SELECT id FROM qr_codes WHERE short_code = ? AND is_active = 1", [$short_code]);
            $qr_code = $stmt->fetch();
            
            if (!$qr_code) {
                return false;
            }
            
            // Insert scan record
            $sql = "INSERT INTO qr_scans (qr_code_id, ip_address, user_agent, referer) VALUES (?, ?, ?, ?)";
            $this->db->query($sql, [
                $qr_code['id'],
                $ip_address ?: $_SERVER['REMOTE_ADDR'] ?? null,
                $user_agent ?: $_SERVER['HTTP_USER_AGENT'] ?? null,
                $referer ?: $_SERVER['HTTP_REFERER'] ?? null
            ]);
            
            // Update scan count
            $sql = "UPDATE qr_codes SET scan_count = scan_count + 1 WHERE id = ?";
            $this->db->query($sql, [$qr_code['id']]);
            
            return true;
            
        } catch (Exception $e) {
            error_log("QR Code scan tracking error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get QR code by short code
     */
    public function getQRCodeByShortCode($short_code) {
        $stmt = $this->db->query("SELECT * FROM qr_codes WHERE short_code = ? AND is_active = 1", [$short_code]);
        return $stmt->fetch();
    }
}

/**
 * Helper function to sanitize filename
 */
function sanitizeFilename($filename) {
    // Remove special characters and spaces
    $filename = preg_replace('/[^a-zA-Z0-9\-_]/', '_', $filename);
    // Remove multiple underscores
    $filename = preg_replace('/_+/', '_', $filename);
    // Trim underscores from start and end
    return trim($filename, '_');
}
?>
