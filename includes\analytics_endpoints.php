<?php
/**
 * Advanced Analytics Endpoints
 * Handles AJAX requests for advanced analytics tracking
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

require_once '../config/config.php';
require_once '../config/database.php';
require_once '../classes/Analytics.php';

try {
    // Only accept POST requests
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Only POST requests allowed');
    }
    
    // Get action from query parameter
    $action = $_GET['action'] ?? '';
    
    // Get JSON input
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        throw new Exception('Invalid JSON input');
    }
    
    $analytics = new Analytics();
    $success = false;
    
    switch ($action) {
        case 'track_journey':
            $success = $analytics->trackJourneyStep(
                $input['url'] ?? '',
                $input['title'] ?? null,
                $input['artist_id'] ?? null
            );
            break;
            
        case 'track_gallery':
            $success = $analytics->trackGalleryInteraction(
                $input['artist_id'] ?? 0,
                $input['interaction_type'] ?? 'view',
                $input['gallery_image_id'] ?? null,
                $input['x_coordinate'] ?? null,
                $input['y_coordinate'] ?? null,
                $input['interaction_data'] ?? null
            );
            break;
            
        case 'track_social':
            $success = $analytics->trackSocialClick(
                $input['artist_id'] ?? 0,
                $input['platform'] ?? 'unknown',
                $input['url'] ?? '',
                $input['position'] ?? null
            );
            break;
            
        case 'track_conversion':
            $success = $analytics->trackConversionStep(
                $input['step'] ?? 'landing',
                $input['artist_id'] ?? null,
                $input['data'] ?? null
            );
            break;
            
        case 'track_heatmap':
            if (isset($input['heatmap_data']) && is_array($input['heatmap_data'])) {
                $success = true;
                foreach ($input['heatmap_data'] as $point) {
                    $result = $analytics->trackHeatmapData(
                        $_SERVER['HTTP_REFERER'] ?? '',
                        $point['element'] ?? null,
                        $point['type'] ?? 'click',
                        $point['x'] ?? 0,
                        $point['y'] ?? 0,
                        $point['viewport_width'] ?? 0,
                        $point['viewport_height'] ?? 0,
                        $input['artist_id'] ?? null
                    );
                    if (!$result) $success = false;
                }
            }
            break;
            
        case 'track_exit':
            // Update the current journey step with exit data
            if (!session_id()) session_start();
            $session_id = session_id();
            
            $db = Database::getInstance();
            $sql = "UPDATE analytics_user_journeys 
                    SET exit_time = NOW(), 
                        time_spent = ?,
                        scroll_depth = ?,
                        interactions_count = ?
                    WHERE session_id = ? AND exit_time IS NULL";
            
            $success = $db->query($sql, [
                $input['time_spent'] ?? 0,
                $input['scroll_depth'] ?? 0,
                $input['interactions'] ?? 0,
                $session_id
            ]);
            break;
            
        case 'track_performance':
            if (!session_id()) session_start();
            $session_id = session_id();
            
            $visitor_ip = $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1';
            $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? '';
            $visitor_hash = hash('sha256', $visitor_ip . $user_agent . date('Y-m-d'));
            
            $db = Database::getInstance();
            $sql = "INSERT INTO analytics_page_performance 
                    (session_id, visitor_hash, page_url, artist_id, load_time, 
                     time_to_first_byte, dom_content_loaded) 
                    VALUES (?, ?, ?, ?, ?, ?, ?)";
            
            $success = $db->query($sql, [
                $session_id,
                $visitor_hash,
                $_SERVER['HTTP_REFERER'] ?? '',
                $input['artist_id'] ?? null,
                $input['load_time'] ?? 0,
                $input['time_to_first_byte'] ?? null,
                $input['dom_content_loaded'] ?? null
            ]);
            break;
            
        default:
            throw new Exception('Invalid action');
    }
    
    if ($success) {
        echo json_encode([
            'success' => true,
            'message' => 'Analytics data tracked successfully'
        ]);
    } else {
        throw new Exception('Failed to track analytics data');
    }
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}
?>
