/* Artist Portfolio Management System - Custom Styles */

/* Import Bulma CSS Framework */
@import url('https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css');
@import url('https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css');

/* Custom Variables */
:root {
    --primary-color: #3273dc;
    --secondary-color: #23d160;
    --accent-color: #ff3860;
    --dark-color: #363636;
    --light-color: #f5f5f5;
    --border-radius: 8px;
    --box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
}

/* Global Styles */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: var(--dark-color);
}

/* Custom Button Styles */
.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    transition: var(--transition);
}

.btn-primary:hover {
    background-color: #2366d1;
    border-color: #2366d1;
    transform: translateY(-2px);
}

/* Card Enhancements */
.card {
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    border: none;
}

.card:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
}

/* Artist Card Specific Styles */
.artist-card {
    margin-bottom: 2rem;
    overflow: hidden;
}

.artist-avatar {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    object-fit: cover;
    border: 4px solid #fff;
    box-shadow: var(--box-shadow);
    margin: 0 auto;
    display: block;
}

.artist-name {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--primary-color);
    margin: 1rem 0 0.5rem;
    text-align: center;
}

.artist-country {
    color: #666;
    font-size: 0.9rem;
    text-align: center;
    margin-bottom: 1rem;
}

.artist-medium {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.8rem;
    display: inline-block;
    margin: 0.25rem;
}

/* Social Media Icons */
.social-links {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin: 1.5rem 0;
}

.social-link {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    color: white;
    text-decoration: none;
    transition: var(--transition);
    font-size: 1.2rem;
}

.social-link:hover {
    transform: scale(1.1);
    box-shadow: var(--box-shadow);
}

.social-link.facebook { background-color: #1877f2; }
.social-link.instagram { background: linear-gradient(45deg, #f09433, #e6683c, #dc2743, #cc2366, #bc1888); }
.social-link.youtube { background-color: #ff0000; }
.social-link.website { background-color: var(--dark-color); }
.social-link.twitter { background-color: #1da1f2; }
.social-link.linkedin { background-color: #0077b5; }

/* Gallery Styles */
.gallery-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin: 2rem 0;
}

.gallery-item {
    position: relative;
    border-radius: var(--border-radius);
    overflow: hidden;
    aspect-ratio: 1;
}

.gallery-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.gallery-item:hover img {
    transform: scale(1.05);
}

/* Admin Dashboard Styles */
.admin-header {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    padding: 2rem 0;
    margin-bottom: 2rem;
}

.admin-sidebar {
    background-color: #f8f9fa;
    min-height: calc(100vh - 120px);
    padding: 1.5rem;
    border-radius: var(--border-radius);
}

.admin-nav-item {
    display: block;
    padding: 0.75rem 1rem;
    color: var(--dark-color);
    text-decoration: none;
    border-radius: var(--border-radius);
    margin-bottom: 0.5rem;
    transition: var(--transition);
}

.admin-nav-item:hover,
.admin-nav-item.active {
    background-color: var(--primary-color);
    color: white;
}

/* Form Enhancements */
.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    font-weight: 600;
    margin-bottom: 0.5rem;
    display: block;
    color: var(--dark-color);
}

.form-control {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid #e1e5e9;
    border-radius: var(--border-radius);
    font-size: 1rem;
    transition: var(--transition);
}

.form-control:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(50, 115, 220, 0.1);
}

/* QR Code Styles */
.qr-code-container {
    text-align: center;
    padding: 2rem;
    background-color: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
}

.qr-code {
    max-width: 300px;
    height: auto;
    margin: 1rem auto;
    display: block;
}

/* Responsive Design */
@media (max-width: 768px) {
    .artist-avatar {
        width: 100px;
        height: 100px;
    }
    
    .gallery-grid {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 0.5rem;
    }
    
    .social-links {
        gap: 0.5rem;
    }
    
    .social-link {
        width: 35px;
        height: 35px;
        font-size: 1rem;
    }
}

/* Loading Animation */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Artist Profile Page Styles */
.artist-profile .artist-avatar-large {
    width: 256px;
    height: 256px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 4rem;
    font-weight: bold;
    margin: 0 auto;
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.artist-profile .gallery-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-top: 2rem;
}

.artist-profile .gallery-item {
    cursor: pointer;
    transition: transform 0.3s ease;
    border-radius: 8px;
    overflow: hidden;
}

.artist-profile .gallery-item:hover {
    transform: translateY(-5px);
}

.artist-profile .gallery-item img {
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    transition: box-shadow 0.3s ease;
    width: 100%;
    height: 200px;
    object-fit: cover;
}

.artist-profile .gallery-item:hover img {
    box-shadow: 0 8px 16px rgba(0,0,0,0.2);
}

.artist-profile .social-links {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
    justify-content: flex-start;
}

.artist-profile .social-link {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 45px;
    height: 45px;
    border-radius: 50%;
    color: white;
    text-decoration: none;
    transition: transform 0.3s ease;
}

.artist-profile .social-link:hover {
    transform: scale(1.1);
    color: white;
}

.artist-profile .social-link.website { background: #333; }
.artist-profile .social-link.instagram { background: linear-gradient(45deg, #f09433 0%,#e6683c 25%,#dc2743 50%,#cc2366 75%,#bc1888 100%); }
.artist-profile .social-link.facebook { background: #1877f2; }
.artist-profile .social-link.youtube { background: #ff0000; }
.artist-profile .social-link.twitter { background: #1da1f2; }
.artist-profile .social-link.linkedin { background: #0077b5; }

/* Enhanced Title and Subtitle Spacing - Reduced by 50% */
.title + .subtitle,
.title + p.subtitle,
h1 + .subtitle,
h2 + .subtitle,
h3 + .subtitle,
h4 + .subtitle,
h5 + .subtitle,
h6 + .subtitle {
    margin-top: 0.5rem !important;
}

/* Specific spacing for different title sizes */
.title.is-1 + .subtitle { margin-top: 0.75rem !important; }
.title.is-2 + .subtitle { margin-top: 0.625rem !important; }
.title.is-3 + .subtitle { margin-top: 0.625rem !important; }
.title.is-4 + .subtitle { margin-top: 0.5rem !important; }
.title.is-5 + .subtitle { margin-top: 0.5rem !important; }
.title.is-6 + .subtitle { margin-top: 0.375rem !important; }

/* Enhanced spacing for page headers */
.page-header .title {
    margin-bottom: 0.75rem !important;
}

.page-header .subtitle {
    margin-top: 0.75rem !important;
    margin-bottom: 1rem !important;
}

/* Section title spacing */
.section-title {
    margin-bottom: 1rem !important;
}

.section-title + .subtitle,
.section-title + p {
    margin-top: 0.625rem !important;
}

/* Admin panel specific spacing */
.admin-page .title {
    margin-bottom: 0.75rem !important;
}

.admin-page .title + .subtitle {
    margin-top: 0.75rem !important;
}

/* Box and card title spacing */
.box .title,
.card .title {
    margin-bottom: 0.75rem !important;
}

.box .title + .subtitle,
.card .title + .subtitle {
    margin-top: 0.625rem !important;
}

/* Utility Classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }
.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: 0.5rem; }
.mb-2 { margin-bottom: 1rem; }
.mb-3 { margin-bottom: 1.5rem; }
.mb-4 { margin-bottom: 2rem; }
.mb-5 { margin-bottom: 2.5rem; }
.mb-6 { margin-bottom: 3rem; }
.mt-0 { margin-top: 0; }
.mt-1 { margin-top: 0.5rem; }
.mt-2 { margin-top: 1rem; }
.mt-3 { margin-top: 1.5rem; }
.mt-4 { margin-top: 2rem; }
.mt-5 { margin-top: 2.5rem; }
.mt-6 { margin-top: 3rem; }

/* Print Styles */
@media print {
    .no-print {
        display: none !important;
    }
    
    .qr-code-container {
        box-shadow: none;
        border: 1px solid #ccc;
    }
}
