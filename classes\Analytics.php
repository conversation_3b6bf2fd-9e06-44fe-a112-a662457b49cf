<?php
/**
 * Analytics Tracking System
 * Tracks user visits, engagement, and provides insights
 */

class Analytics {
    private $db;
    
    public function __construct() {
        $this->db = Database::getInstance();
    }
    
    /**
     * Track a page visit
     */
    public function trackVisit($artist_id = null, $page_type = 'artist') {
        try {
            $visitor_data = $this->getVisitorData();
            $session_id = $this->getOrCreateSession($visitor_data);
            
            // Insert visit record
            $sql = "INSERT INTO analytics_visits (
                artist_id, page_type, visitor_ip, visitor_hash, user_agent, 
                referrer, country, city, device_type, browser, session_id
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
            
            $this->db->query($sql, [
                $artist_id,
                $page_type,
                $visitor_data['ip'],
                $visitor_data['hash'],
                $visitor_data['user_agent'],
                $visitor_data['referrer'],
                $visitor_data['country'],
                $visitor_data['city'],
                $visitor_data['device_type'],
                $visitor_data['browser'],
                $session_id
            ]);
            
            // Update session
            $this->updateSession($session_id, $artist_id);
            
            // Update artist stats if artist page
            if ($artist_id) {
                $this->updateArtistStats($artist_id);
            }
            
            // Track traffic source
            $this->trackTrafficSource($visitor_data['referrer']);
            
            return true;
            
        } catch (Exception $e) {
            error_log("Analytics tracking error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get visitor data from request
     */
    private function getVisitorData() {
        $ip = $this->getClientIP();
        $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? '';
        $referrer = $_SERVER['HTTP_REFERER'] ?? '';
        
        return [
            'ip' => $ip,
            'hash' => hash('sha256', $ip . $user_agent . date('Y-m-d')), // Daily unique hash
            'user_agent' => $user_agent,
            'referrer' => $referrer,
            'country' => $this->getCountryFromIP($ip),
            'city' => $this->getCityFromIP($ip),
            'device_type' => $this->getDeviceType($user_agent),
            'browser' => $this->getBrowser($user_agent)
        ];
    }
    
    /**
     * Get or create user session
     */
    private function getOrCreateSession($visitor_data) {
        if (!session_id()) {
            session_start();
        }
        
        $session_id = session_id();
        
        // Check if session exists
        $stmt = $this->db->query("SELECT id FROM analytics_sessions WHERE session_id = ?", [$session_id]);
        
        if (!$stmt->fetch()) {
            // Create new session
            $sql = "INSERT INTO analytics_sessions (
                session_id, visitor_hash, device_type, browser, country
            ) VALUES (?, ?, ?, ?, ?)";
            
            $this->db->query($sql, [
                $session_id,
                $visitor_data['hash'],
                $visitor_data['device_type'],
                $visitor_data['browser'],
                $visitor_data['country']
            ]);
        }
        
        return $session_id;
    }
    
    /**
     * Update session data
     */
    private function updateSession($session_id, $artist_id = null) {
        $sql = "UPDATE analytics_sessions SET 
                pages_viewed = pages_viewed + 1,
                end_time = NOW()";
        
        $params = [$session_id];
        
        if ($artist_id) {
            $sql .= ", artists_viewed = artists_viewed + 1";
        }
        
        $sql .= " WHERE session_id = ?";
        
        $this->db->query($sql, $params);
    }
    
    /**
     * Update artist statistics
     */
    private function updateArtistStats($artist_id) {
        $sql = "INSERT INTO analytics_artist_stats (artist_id, total_views, unique_visitors) 
                VALUES (?, 1, 1)
                ON DUPLICATE KEY UPDATE 
                total_views = total_views + 1,
                unique_visitors = (
                    SELECT COUNT(DISTINCT visitor_hash) 
                    FROM analytics_visits 
                    WHERE artist_id = ?
                )";
        
        $this->db->query($sql, [$artist_id, $artist_id]);
    }
    
    /**
     * Track traffic source
     */
    private function trackTrafficSource($referrer) {
        if (empty($referrer)) {
            $source_type = 'direct';
            $source_domain = null;
        } else {
            $parsed = parse_url($referrer);
            $source_domain = $parsed['host'] ?? null;
            $source_type = $this->categorizeTrafficSource($source_domain);
        }
        
        $sql = "INSERT INTO analytics_traffic_sources (source_type, source_domain, source_url, visit_count, unique_visitors) 
                VALUES (?, ?, ?, 1, 1)
                ON DUPLICATE KEY UPDATE 
                visit_count = visit_count + 1,
                unique_visitors = unique_visitors + 1";
        
        $this->db->query($sql, [$source_type, $source_domain, $referrer]);
    }
    
    /**
     * Get client IP address
     */
    private function getClientIP() {
        $ip_keys = ['HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR'];
        
        foreach ($ip_keys as $key) {
            if (!empty($_SERVER[$key])) {
                $ip = trim(explode(',', $_SERVER[$key])[0]);
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }
        
        return $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1';
    }
    
    /**
     * Get country from IP (simplified - in production use GeoIP service)
     */
    private function getCountryFromIP($ip) {
        // Simplified country detection
        if ($ip === '127.0.0.1' || strpos($ip, '192.168.') === 0) {
            return 'Local';
        }
        
        // In production, integrate with GeoIP service like MaxMind
        return 'Unknown';
    }
    
    /**
     * Get city from IP (simplified)
     */
    private function getCityFromIP($ip) {
        if ($ip === '127.0.0.1' || strpos($ip, '192.168.') === 0) {
            return 'Localhost';
        }
        
        return 'Unknown';
    }
    
    /**
     * Detect device type from user agent
     */
    private function getDeviceType($user_agent) {
        if (preg_match('/Mobile|Android|iPhone|iPad/', $user_agent)) {
            if (preg_match('/iPad/', $user_agent)) {
                return 'Tablet';
            }
            return 'Mobile';
        }
        return 'Desktop';
    }
    
    /**
     * Detect browser from user agent
     */
    private function getBrowser($user_agent) {
        if (preg_match('/Chrome/', $user_agent)) return 'Chrome';
        if (preg_match('/Firefox/', $user_agent)) return 'Firefox';
        if (preg_match('/Safari/', $user_agent)) return 'Safari';
        if (preg_match('/Edge/', $user_agent)) return 'Edge';
        if (preg_match('/Opera/', $user_agent)) return 'Opera';
        return 'Other';
    }
    
    /**
     * Categorize traffic source
     */
    private function categorizeTrafficSource($domain) {
        if (!$domain) return 'direct';
        
        $social_domains = ['facebook.com', 'instagram.com', 'twitter.com', 'linkedin.com', 'pinterest.com'];
        $search_domains = ['google.com', 'bing.com', 'yahoo.com', 'duckduckgo.com'];
        
        foreach ($social_domains as $social) {
            if (strpos($domain, $social) !== false) return 'social';
        }
        
        foreach ($search_domains as $search) {
            if (strpos($domain, $search) !== false) return 'search';
        }
        
        return 'referral';
    }
}
