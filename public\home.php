<?php
/**
 * Home Page - Artist Portfolio Management System
 */

// Page configuration
$page_title = 'Welcome to ' . SITE_NAME;
$page_description = 'Discover amazing artists and their beautiful portfolios. Browse through curated collections of art from talented creators around the world.';
$page_keywords = 'artists, portfolio, gallery, art, creative, showcase';
$body_class = 'home-page';

// Include header
include_once 'includes/header.php';

// Get featured artists (if database is available)
$featured_artists = [];
try {
    if (file_exists('config/installed.lock')) {
        $featured_artists = $language->getFeaturedArtistsWithTranslations(6);
    }
} catch (Exception $e) {
    // Database not available yet
}
?>

<!-- Hero Section -->
<section class="hero is-primary is-large">
    <div class="hero-body">
        <div class="container has-text-centered">
            <h1 class="title is-1">
                <span class="icon is-large">
                    <i class="fas fa-palette"></i>
                </span>
                Welcome to <?php echo SITE_NAME; ?>
            </h1>
            <h2 class="subtitle is-3">
                Discover Amazing Artists & Their Beautiful Portfolios
            </h2>
            <p class="is-size-5 mb-5">
                Explore curated collections of art from talented creators around the world. 
                Each artist has their own unique style and story to tell.
            </p>
            
            <?php if (file_exists('config/installed.lock')): ?>
            <div class="buttons is-centered">
                <a href="?page=artists" class="button is-white is-large">
                    <span class="icon"><i class="fas fa-users"></i></span>
                    <span>Browse Artists</span>
                </a>
                <a href="admin/login.php" class="button is-light is-large">
                    <span class="icon"><i class="fas fa-cog"></i></span>
                    <span>Admin Panel</span>
                </a>
            </div>
            <?php else: ?>
            <div class="buttons is-centered">
                <a href="install/install.php" class="button is-white is-large">
                    <span class="icon"><i class="fas fa-download"></i></span>
                    <span>Install System</span>
                </a>
            </div>
            <?php endif; ?>
        </div>
    </div>
</section>

<?php if (!file_exists('config/installed.lock')): ?>
<!-- Installation Notice -->
<section class="section">
    <div class="container">
        <div class="notification is-warning is-light">
            <h3 class="title is-4">
                <span class="icon"><i class="fas fa-exclamation-triangle"></i></span>
                System Not Installed
            </h3>
            <p class="mb-4">
                Welcome! It looks like this is your first time here. To get started with your Artist Portfolio Management System, 
                you'll need to run the installation process.
            </p>
            <div class="content">
                <h4>What the installation will do:</h4>
                <ul>
                    <li>Set up the MySQL database with all required tables</li>
                    <li>Create your admin user account</li>
                    <li>Configure basic site settings</li>
                    <li>Set up file upload directories</li>
                    <li>Generate security configurations</li>
                </ul>
            </div>
            <a href="install/install.php" class="button is-warning is-large">
                <span class="icon"><i class="fas fa-rocket"></i></span>
                <span>Start Installation</span>
            </a>
        </div>
    </div>
</section>

<?php else: ?>
<!-- Features Section -->
<section class="section">
    <div class="container">
        <div class="has-text-centered mb-6">
            <h2 class="title is-2">Powerful Features</h2>
            <p class="subtitle">Everything you need to showcase artists beautifully</p>
        </div>
        
        <div class="columns is-multiline">
            <div class="column is-4">
                <div class="card">
                    <div class="card-content has-text-centered">
                        <span class="icon is-large has-text-primary">
                            <i class="fas fa-mobile-alt fa-3x"></i>
                        </span>
                        <h3 class="title is-4">Mobile Optimized</h3>
                        <p>Every artist page is perfectly optimized for both mobile and desktop viewing, ensuring a great experience on any device.</p>
                    </div>
                </div>
            </div>
            
            <div class="column is-4">
                <div class="card">
                    <div class="card-content has-text-centered">
                        <span class="icon is-large has-text-primary">
                            <i class="fas fa-qrcode fa-3x"></i>
                        </span>
                        <h3 class="title is-4">QR Code Generation</h3>
                        <p>Automatically generate QR codes for each artist page with tracking capabilities and downloadable formats.</p>
                    </div>
                </div>
            </div>
            
            <div class="column is-4">
                <div class="card">
                    <div class="card-content has-text-centered">
                        <span class="icon is-large has-text-primary">
                            <i class="fas fa-chart-line fa-3x"></i>
                        </span>
                        <h3 class="title is-4">Analytics Tracking</h3>
                        <p>Track page views, QR code scans, and visitor analytics to understand how people engage with artist portfolios.</p>
                    </div>
                </div>
            </div>
            
            <div class="column is-4">
                <div class="card">
                    <div class="card-content has-text-centered">
                        <span class="icon is-large has-text-primary">
                            <i class="fas fa-link fa-3x"></i>
                        </span>
                        <h3 class="title is-4">Short URLs</h3>
                        <p>Create memorable short URLs for each artist page that are easy to share and remember.</p>
                    </div>
                </div>
            </div>
            
            <div class="column is-4">
                <div class="card">
                    <div class="card-content has-text-centered">
                        <span class="icon is-large has-text-primary">
                            <i class="fas fa-share-alt fa-3x"></i>
                        </span>
                        <h3 class="title is-4">Social Integration</h3>
                        <p>Connect artist social media profiles including Instagram, Facebook, YouTube, and personal websites.</p>
                    </div>
                </div>
            </div>
            
            <div class="column is-4">
                <div class="card">
                    <div class="card-content has-text-centered">
                        <span class="icon is-large has-text-primary">
                            <i class="fas fa-images fa-3x"></i>
                        </span>
                        <h3 class="title is-4">Gallery Management</h3>
                        <p>Upload and organize artwork in beautiful galleries with support for multiple image formats and sizes.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<?php if ($featured_artists): ?>
<!-- Featured Artists Section -->
<section class="section has-background-light">
    <div class="container">
        <div class="has-text-centered mb-6">
            <h2 class="title is-2">Featured Artists</h2>
            <p class="subtitle">Discover some of our amazing featured artists</p>
        </div>
        
        <div class="columns is-multiline">
            <?php foreach ($featured_artists as $artist): ?>
            <div class="column is-4">
                <div class="card artist-card">
                    <div class="card-content">
                        <?php if ($artist['avatar_image']): ?>
                        <img src="<?php echo htmlspecialchars($artist['avatar_image']); ?>" 
                             alt="<?php echo htmlspecialchars($artist['name']); ?>" 
                             class="artist-avatar">
                        <?php else: ?>
                        <div class="artist-avatar" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); display: flex; align-items: center; justify-content: center; color: white; font-size: 2rem; font-weight: bold;">
                            <?php echo strtoupper(substr($artist['name'], 0, 1)); ?>
                        </div>
                        <?php endif; ?>
                        
                        <h3 class="artist-name"><?php echo htmlspecialchars($artist['name']); ?></h3>
                        
                        <?php if ($artist['country']): ?>
                        <p class="artist-country">
                            <span class="icon"><i class="fas fa-map-marker-alt"></i></span>
                            <?php echo htmlspecialchars($artist['country']); ?>
                        </p>
                        <?php endif; ?>
                        
                        <?php if ($artist['main_medium']): ?>
                        <span class="artist-medium"><?php echo htmlspecialchars($artist['main_medium']); ?></span>
                        <?php endif; ?>
                        
                        <?php if ($artist['bio']): ?>
                        <p class="mt-3"><?php echo htmlspecialchars(substr($artist['bio'], 0, 100)) . (strlen($artist['bio']) > 100 ? '...' : ''); ?></p>
                        <?php endif; ?>
                        
                        <div class="has-text-centered mt-4">
                            <a href="?page=artist&slug=<?php echo htmlspecialchars($artist['slug']); ?>" class="button is-primary">
                                <span class="icon"><i class="fas fa-eye"></i></span>
                                <span>View Portfolio</span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
        
        <div class="has-text-centered mt-6">
            <a href="?page=artists" class="button is-primary is-large">
                <span class="icon"><i class="fas fa-users"></i></span>
                <span>View All Artists</span>
            </a>
        </div>
    </div>
</section>
<?php endif; ?>

<!-- Call to Action Section -->
<section class="section">
    <div class="container">
        <div class="box has-background-primary">
            <div class="has-text-centered has-text-white">
                <h2 class="title is-2 has-text-white">Ready to Get Started?</h2>
                <p class="subtitle is-4 has-text-white">
                    Create beautiful artist portfolios with QR codes, analytics, and more.
                </p>
                <a href="admin/login.php" class="button is-white is-large">
                    <span class="icon"><i class="fas fa-rocket"></i></span>
                    <span>Access Admin Panel</span>
                </a>
            </div>
        </div>
    </div>
</section>
<?php endif; ?>

<?php
// Include footer
include_once 'includes/footer.php';
?>
