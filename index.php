<?php
/**
 * Artist Portfolio Management System
 * Main entry point and router
 */

session_start();

// Configuration
require_once 'config/config.php';
require_once 'config/database.php';

// Auto-load classes
spl_autoload_register(function ($class) {
    $file = 'classes/' . str_replace('\\', '/', $class) . '.php';
    if (file_exists($file)) {
        require_once $file;
    }
});

// Initialize language system
$language = new Language();

// SEO-Friendly Router
$request = $_GET['page'] ?? '';
$action = $_GET['action'] ?? 'index';

// Handle short URL redirects
if (isset($_GET['u'])) {
    require_once 'includes/redirect.php';
    exit;
}

// Route to appropriate controller
switch ($request) {
    case 'admin':
        require_once 'admin/index.php';
        break;
    case 'artist':
        // SEO-friendly artist profiles
        if (isset($_GET['slug']) && !empty($_GET['slug'])) {
            // Validate slug format (alphanumeric and hyphens only)
            if (preg_match('/^[a-zA-Z0-9\-]+$/', $_GET['slug'])) {
                require_once 'public/artist.php';
            } else {
                // Invalid slug format - redirect to admin
                header('Location: ' . SITE_URL . '/admin/login.php', true, 301);
                exit;
            }
        } else {
            // No slug provided - redirect to admin
            header('Location: ' . SITE_URL . '/admin/login.php', true, 301);
            exit;
        }
        break;
    case 'qr':
        // Handle QR code redirects: /qr/{short_code}
        $short_code = $_GET['code'] ?? '';
        if (!empty($short_code)) {
            $_GET['code'] = $short_code;
            require_once 'includes/qr_track.php';
        } else {
            // No code provided - redirect to admin
            header('Location: ' . SITE_URL . '/admin/qr_codes.php');
            exit;
        }
        break;
    default:
        // No public home page - redirect to admin
        header('Location: ' . SITE_URL . '/admin/login.php', true, 301);
        exit;
}
