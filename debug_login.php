<?php
/**
 * Login Debug Script
 * Use this to debug login issues on live server
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Login Debug</h1>";

// Include required files
if (file_exists('config/config.php')) {
    require_once 'config/config.php';
    echo "✅ Config loaded<br>";
} else {
    echo "❌ Config not found<br>";
    exit;
}

if (file_exists('config/database.php')) {
    require_once 'config/database.php';
    echo "✅ Database class loaded<br>";
} else {
    echo "❌ Database class not found<br>";
    exit;
}

if (file_exists('classes/User.php')) {
    require_once 'classes/User.php';
    echo "✅ User class loaded<br>";
} else {
    echo "❌ User class not found<br>";
    exit;
}

// Test login process
if (isset($_POST['test_login'])) {
    $username = $_POST['username'] ?? '';
    $password = $_POST['password'] ?? '';
    
    echo "<h2>Testing Login for: {$username}</h2>";
    
    try {
        // Test database connection
        $db = Database::getInstance();
        echo "✅ Database connected<br>";
        
        // Test user lookup
        $stmt = $db->query("SELECT * FROM users WHERE username = ? OR email = ?", [$username, $username]);
        $user = $stmt->fetch();
        
        if ($user) {
            echo "✅ User found in database<br>";
            echo "- ID: {$user['id']}<br>";
            echo "- Username: {$user['username']}<br>";
            echo "- Email: {$user['email']}<br>";
            echo "- Role: {$user['role']}<br>";
            echo "- Active: " . ($user['is_active'] ? 'Yes' : 'No') . "<br>";
            echo "- Password Hash: " . substr($user['password'], 0, 20) . "...<br>";
            
            // Test password verification
            if (password_verify($password, $user['password'])) {
                echo "✅ Password verification successful<br>";
                
                // Test User class login
                $userObj = new User();
                $loginResult = $userObj->login($username, $password);
                
                if ($loginResult) {
                    echo "✅ User class login successful<br>";
                    echo "- User ID: {$loginResult['id']}<br>";
                    echo "- Role: {$loginResult['role']}<br>";
                    
                    // Test session
                    session_start();
                    if (isset($_SESSION['user_id'])) {
                        echo "✅ Session created successfully<br>";
                        echo "- Session ID: " . session_id() . "<br>";
                        echo "- User ID in session: {$_SESSION['user_id']}<br>";
                    } else {
                        echo "❌ Session not created<br>";
                    }
                } else {
                    echo "❌ User class login failed<br>";
                }
            } else {
                echo "❌ Password verification failed<br>";
                echo "- Entered password: {$password}<br>";
                echo "- Try common passwords: admin, admin123, password<br>";
            }
        } else {
            echo "❌ User not found in database<br>";
            echo "- Searched for: {$username}<br>";
            echo "- Check if username/email is correct<br>";
        }
        
    } catch (Exception $e) {
        echo "❌ Error during login test: " . $e->getMessage() . "<br>";
        echo "Stack trace: " . $e->getTraceAsString() . "<br>";
    }
}

// Show login form
echo "<h2>Test Login</h2>";
echo "<form method='POST'>";
echo "<p>";
echo "<label>Username/Email:</label><br>";
echo "<input type='text' name='username' value='admin' style='padding: 5px; width: 200px;'>";
echo "</p>";
echo "<p>";
echo "<label>Password:</label><br>";
echo "<input type='password' name='password' value='admin123' style='padding: 5px; width: 200px;'>";
echo "</p>";
echo "<button type='submit' name='test_login' style='background: #3498db; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;'>Test Login</button>";
echo "</form>";

echo "<h2>All Users in Database:</h2>";
try {
    $db = Database::getInstance();
    $stmt = $db->query("SELECT id, username, email, role, is_active FROM users");
    $users = $stmt->fetchAll();
    
    if ($users) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>ID</th><th>Username</th><th>Email</th><th>Role</th><th>Active</th></tr>";
        foreach ($users as $user) {
            echo "<tr>";
            echo "<td>{$user['id']}</td>";
            echo "<td>{$user['username']}</td>";
            echo "<td>{$user['email']}</td>";
            echo "<td>{$user['role']}</td>";
            echo "<td>" . ($user['is_active'] ? 'Yes' : 'No') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>No users found.</p>";
    }
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "<br>";
}

echo "<p><a href='admin/login.php'>Go to Real Login Page</a></p>";
?>
