-- Artist Portfolio Database Structure
-- Import this into your cPanel database

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
SET AUTOCOMMIT = 0;
START TRANSACTION;
SET time_zone = "+00:00";

-- Create users table
CREATE TABLE IF NOT EXISTS `users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL,
  `email` varchar(100) NOT NULL,
  `password` varchar(255) NOT NULL,
  `role` enum('admin','user') NOT NULL DEFAULT 'user',
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`),
  UNIQUE KEY `email` (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Insert default admin user (password: admin123)
INSERT INTO `users` (`username`, `email`, `password`, `role`, `is_active`) VALUES
('admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'admin', 1);

-- Create artists table
CREATE TABLE IF NOT EXISTS `artists` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `slug` varchar(100) NOT NULL,
  `bio` text,
  `avatar_image` varchar(255) DEFAULT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `slug` (`slug`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Create artist_galleries table
CREATE TABLE IF NOT EXISTS `artist_galleries` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `artist_id` int(11) NOT NULL,
  `title` varchar(200) DEFAULT NULL,
  `description` text,
  `image_path` varchar(255) NOT NULL,
  `sort_order` int(11) DEFAULT 0,
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `artist_id` (`artist_id`),
  FOREIGN KEY (`artist_id`) REFERENCES `artists` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Create artist_social_links table
CREATE TABLE IF NOT EXISTS `artist_social_links` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `artist_id` int(11) NOT NULL,
  `platform` varchar(50) NOT NULL,
  `url` varchar(255) NOT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `artist_id` (`artist_id`),
  FOREIGN KEY (`artist_id`) REFERENCES `artists` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Create analytics_visits table
CREATE TABLE IF NOT EXISTS `analytics_visits` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `artist_id` int(11) DEFAULT NULL,
  `visitor_ip` varchar(45) DEFAULT NULL,
  `visitor_hash` varchar(64) DEFAULT NULL,
  `user_agent` text,
  `referer` varchar(500) DEFAULT NULL,
  `visit_type` varchar(50) DEFAULT 'page_view',
  `country` varchar(100) DEFAULT NULL,
  `city` varchar(100) DEFAULT NULL,
  `age_range` varchar(20) DEFAULT NULL,
  `gender` varchar(10) DEFAULT NULL,
  `consent_given` tinyint(1) DEFAULT 0,
  `device_type` varchar(50) DEFAULT NULL,
  `browser` varchar(50) DEFAULT NULL,
  `visited_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `artist_id` (`artist_id`),
  KEY `visited_at` (`visited_at`),
  FOREIGN KEY (`artist_id`) REFERENCES `artists` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Create analytics_user_journeys table
CREATE TABLE IF NOT EXISTS `analytics_user_journeys` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `session_id` varchar(64) NOT NULL,
  `visitor_hash` varchar(64) NOT NULL,
  `page_url` varchar(500) NOT NULL,
  `page_title` varchar(200) DEFAULT NULL,
  `artist_id` int(11) DEFAULT NULL,
  `entry_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `exit_time` timestamp NULL DEFAULT NULL,
  `time_spent` int(11) DEFAULT 0,
  `scroll_depth` int(11) DEFAULT 0,
  `interactions_count` int(11) DEFAULT 0,
  `referrer` varchar(500) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `session_id` (`session_id`),
  KEY `visitor_hash` (`visitor_hash`),
  KEY `artist_id` (`artist_id`),
  KEY `entry_time` (`entry_time`),
  FOREIGN KEY (`artist_id`) REFERENCES `artists` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Create analytics_gallery_interactions table
CREATE TABLE IF NOT EXISTS `analytics_gallery_interactions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `session_id` varchar(64) NOT NULL,
  `visitor_hash` varchar(64) NOT NULL,
  `artist_id` int(11) NOT NULL,
  `gallery_image_id` int(11) DEFAULT NULL,
  `interaction_type` enum('view','click','modal_open','modal_close','navigation','zoom') NOT NULL,
  `interaction_data` json DEFAULT NULL,
  `x_coordinate` int(11) DEFAULT NULL,
  `y_coordinate` int(11) DEFAULT NULL,
  `timestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `session_id` (`session_id`),
  KEY `artist_id` (`artist_id`),
  KEY `interaction_type` (`interaction_type`),
  KEY `timestamp` (`timestamp`),
  FOREIGN KEY (`artist_id`) REFERENCES `artists` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Create analytics_social_clicks table
CREATE TABLE IF NOT EXISTS `analytics_social_clicks` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `session_id` varchar(64) NOT NULL,
  `visitor_hash` varchar(64) NOT NULL,
  `artist_id` int(11) NOT NULL,
  `social_platform` varchar(50) NOT NULL,
  `click_url` varchar(500) NOT NULL,
  `click_position` varchar(50) DEFAULT NULL,
  `timestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `session_id` (`session_id`),
  KEY `artist_id` (`artist_id`),
  KEY `social_platform` (`social_platform`),
  KEY `timestamp` (`timestamp`),
  FOREIGN KEY (`artist_id`) REFERENCES `artists` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Create analytics_conversion_funnels table
CREATE TABLE IF NOT EXISTS `analytics_conversion_funnels` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `session_id` varchar(64) NOT NULL,
  `visitor_hash` varchar(64) NOT NULL,
  `artist_id` int(11) DEFAULT NULL,
  `funnel_step` enum('landing','profile_view','gallery_view','social_click','contact_attempt') NOT NULL,
  `step_data` json DEFAULT NULL,
  `timestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `session_id` (`session_id`),
  KEY `artist_id` (`artist_id`),
  KEY `funnel_step` (`funnel_step`),
  KEY `timestamp` (`timestamp`),
  FOREIGN KEY (`artist_id`) REFERENCES `artists` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Create analytics_heatmap_data table
CREATE TABLE IF NOT EXISTS `analytics_heatmap_data` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `session_id` varchar(64) NOT NULL,
  `visitor_hash` varchar(64) NOT NULL,
  `page_url` varchar(500) NOT NULL,
  `artist_id` int(11) DEFAULT NULL,
  `element_selector` varchar(200) DEFAULT NULL,
  `interaction_type` enum('click','hover','scroll','focus') NOT NULL,
  `x_coordinate` int(11) NOT NULL,
  `y_coordinate` int(11) NOT NULL,
  `viewport_width` int(11) NOT NULL,
  `viewport_height` int(11) NOT NULL,
  `timestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `session_id` (`session_id`),
  KEY `artist_id` (`artist_id`),
  KEY `page_url` (`page_url`(255)),
  KEY `timestamp` (`timestamp`),
  FOREIGN KEY (`artist_id`) REFERENCES `artists` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Create analytics_page_performance table
CREATE TABLE IF NOT EXISTS `analytics_page_performance` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `session_id` varchar(64) NOT NULL,
  `visitor_hash` varchar(64) NOT NULL,
  `page_url` varchar(500) NOT NULL,
  `artist_id` int(11) DEFAULT NULL,
  `load_time` int(11) NOT NULL,
  `time_to_first_byte` int(11) DEFAULT NULL,
  `dom_content_loaded` int(11) DEFAULT NULL,
  `first_paint` int(11) DEFAULT NULL,
  `largest_contentful_paint` int(11) DEFAULT NULL,
  `cumulative_layout_shift` decimal(5,3) DEFAULT NULL,
  `timestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `session_id` (`session_id`),
  KEY `artist_id` (`artist_id`),
  KEY `timestamp` (`timestamp`),
  FOREIGN KEY (`artist_id`) REFERENCES `artists` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

COMMIT;
