<?php
/**
 * Live Server Configuration Template
 * Copy this to config.php and update with your live server details
 */

// Database Configuration - UPDATE THESE FOR YOUR LIVE SERVER
define('DB_HOST', 'localhost'); // Usually 'localhost' for cPanel
define('DB_NAME', 'your_cpanel_username_databasename'); // e.g., 'username_artistcard'
define('DB_USER', 'your_cpanel_username_dbuser'); // e.g., 'username_dbuser'
define('DB_PASS', 'your_database_password'); // Your database password

// Site Configuration - UPDATE THESE FOR YOUR LIVE DOMAIN
define('SITE_URL', 'https://yourdomain.com'); // Your live domain
define('SITE_NAME', 'Artist Portfolio Manager');
define('ADMIN_EMAIL', '<EMAIL>'); // Your admin email

// Security
define('HASH_ALGO', 'sha256');
define('SESSION_TIMEOUT', 3600); // 1 hour

// File Upload Settings
define('UPLOAD_DIR', 'uploads/');
define('MAX_FILE_SIZE', 5 * 1024 * 1024); // 5MB
define('ALLOWED_EXTENSIONS', ['jpg', 'jpeg', 'png', 'gif', 'webp']);

// QR Code Settings
define('QR_CODE_SIZE', 300);
define('QR_CODE_DIR', 'qr_codes/');

// Pagination
define('ITEMS_PER_PAGE', 10);

// Error Reporting for Live Server
error_reporting(E_ALL);
ini_set('display_errors', 1); // Set to 0 in production
ini_set('log_errors', 1);
ini_set('error_log', 'error.log');

// Session Configuration
ini_set('session.cookie_httponly', 1);
ini_set('session.cookie_secure', 1); // Only if using HTTPS
ini_set('session.use_strict_mode', 1);

// Timezone
date_default_timezone_set('UTC'); // Set your timezone

/*
INSTRUCTIONS FOR CPANEL SETUP:

1. Database Configuration:
   - In cPanel, go to MySQL Databases
   - Create a database (e.g., username_artistcard)
   - Create a database user (e.g., username_dbuser)
   - Add the user to the database with ALL PRIVILEGES
   - Update DB_HOST, DB_NAME, DB_USER, DB_PASS above

2. File Permissions:
   - Set uploads/ folder to 755 or 777
   - Set qr_codes/ folder to 755 or 777
   - Set config/ folder to 755

3. Admin User:
   - Make sure you imported the database with admin user
   - Default admin credentials should be in your database
   - Check the 'users' table for admin account

4. Common Issues:
   - Check if all files uploaded correctly
   - Verify database connection
   - Check file permissions
   - Enable error reporting (above) to see errors
*/
?>
