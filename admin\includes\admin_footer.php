    </main>
    
    <!-- JavaScript -->
    <script src="../assets/js/app.js"></script>
    
    <!-- Additional JavaScript -->
    <?php if (isset($additional_js)): ?>
        <?php foreach ($additional_js as $js): ?>
            <script src="<?php echo htmlspecialchars($js); ?>"></script>
        <?php endforeach; ?>
    <?php endif; ?>
    
    <!-- Inline JavaScript -->
    <?php if (isset($inline_js)): ?>
    <script>
        <?php echo $inline_js; ?>
    </script>
    <?php endif; ?>
    
    <!-- Admin-specific JavaScript -->
    <script>
        // Auto-hide flash messages
        document.querySelectorAll('.notification .delete').forEach(btn => {
            btn.addEventListener('click', () => {
                btn.parentElement.style.display = 'none';
            });
        });
        
        // Auto-hide notifications after 5 seconds
        setTimeout(() => {
            document.querySelectorAll('.notification.is-light').forEach(notification => {
                notification.style.opacity = '0';
                setTimeout(() => {
                    notification.style.display = 'none';
                }, 300);
            });
        }, 5000);
        
        // Mobile menu toggle
        document.addEventListener('DOMContentLoaded', () => {
            const burger = document.querySelector('.navbar-burger');
            const menu = document.querySelector('.navbar-menu');
            
            if (burger && menu) {
                burger.addEventListener('click', () => {
                    burger.classList.toggle('is-active');
                    menu.classList.toggle('is-active');
                });
            }
        });
        
        // Custom delete confirmation modal
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('confirm-delete') ||
                e.target.closest('.confirm-delete')) {
                e.preventDefault();

                const deleteButton = e.target.closest('.confirm-delete') || e.target;
                const deleteUrl = deleteButton.getAttribute('data-url');

                if (deleteUrl) {
                    showDeleteModal(deleteUrl);
                }
                return false;
            }
        });

        // Show custom delete modal
        function showDeleteModal(deleteUrl) {
            // Remove existing modal if any
            const existingModal = document.getElementById('delete-modal');
            if (existingModal) {
                existingModal.remove();
            }

            // Create modal HTML
            const modalHTML = `
                <div id="delete-modal" class="modal is-active">
                    <div class="modal-background"></div>
                    <div class="modal-card">
                        <header class="modal-card-head">
                            <p class="modal-card-title">
                                <span class="icon has-text-danger">
                                    <i class="fas fa-exclamation-triangle"></i>
                                </span>
                                Confirm Delete
                            </p>
                        </header>
                        <section class="modal-card-body">
                            <p class="has-text-centered">
                                <span class="icon is-large has-text-danger">
                                    <i class="fas fa-trash fa-2x"></i>
                                </span>
                            </p>
                            <p class="has-text-centered is-size-5 mb-4">
                                Are you sure you want to delete this item?
                            </p>
                            <p class="has-text-centered has-text-grey">
                                This action cannot be undone.
                            </p>
                        </section>
                        <footer class="modal-card-foot is-justify-content-center">
                            <button class="button is-danger" id="confirm-delete-btn">
                                <span class="icon">
                                    <i class="fas fa-trash"></i>
                                </span>
                                <span>Yes, Delete</span>
                            </button>
                            <button class="button" id="cancel-delete-btn">Cancel</button>
                        </footer>
                    </div>
                </div>
            `;

            // Add modal to page
            document.body.insertAdjacentHTML('beforeend', modalHTML);

            // Add event listeners
            document.getElementById('confirm-delete-btn').addEventListener('click', () => {
                window.location.href = deleteUrl;
            });

            document.getElementById('cancel-delete-btn').addEventListener('click', closeDeleteModal);
            document.querySelector('#delete-modal .modal-background').addEventListener('click', closeDeleteModal);

            // Close on Escape key
            document.addEventListener('keydown', function escapeHandler(e) {
                if (e.key === 'Escape') {
                    closeDeleteModal();
                    document.removeEventListener('keydown', escapeHandler);
                }
            });
        }

        // Close delete modal
        function closeDeleteModal() {
            const modal = document.getElementById('delete-modal');
            if (modal) {
                modal.remove();
            }
        }
        
        // Form validation enhancement
        document.addEventListener('submit', (e) => {
            const form = e.target;
            if (form.classList.contains('validate-form')) {
                const requiredFields = form.querySelectorAll('[required]');
                let isValid = true;
                
                requiredFields.forEach(field => {
                    if (!field.value.trim()) {
                        field.classList.add('is-danger');
                        isValid = false;
                    } else {
                        field.classList.remove('is-danger');
                    }
                });
                
                if (!isValid) {
                    e.preventDefault();
                    ArtistPortfolio.showNotification('Please fill in all required fields.', 'error');
                }
            }
        });
        
        // Auto-save functionality for forms
        const autoSaveForms = document.querySelectorAll('.auto-save');
        autoSaveForms.forEach(form => {
            const inputs = form.querySelectorAll('input, textarea, select');
            inputs.forEach(input => {
                input.addEventListener('change', ArtistPortfolio.debounce(() => {
                    // Auto-save logic here
                    console.log('Auto-saving form data...');
                }, 2000));
            });
        });
        
        // Image preview functionality
        document.addEventListener('change', (e) => {
            if (e.target.type === 'file' && e.target.accept && e.target.accept.includes('image')) {
                const file = e.target.files[0];
                if (file) {
                    const reader = new FileReader();
                    reader.onload = (event) => {
                        const previewId = e.target.getAttribute('data-preview');
                        const preview = document.getElementById(previewId);
                        if (preview) {
                            preview.src = event.target.result;
                            preview.style.display = 'block';
                        }
                    };
                    reader.readAsDataURL(file);
                }
            }
        });
        
        // Sortable tables
        const sortableTables = document.querySelectorAll('.sortable-table');
        sortableTables.forEach(table => {
            const headers = table.querySelectorAll('th[data-sort]');
            headers.forEach(header => {
                header.style.cursor = 'pointer';
                header.addEventListener('click', () => {
                    const sortKey = header.getAttribute('data-sort');
                    // Implement sorting logic here
                    console.log('Sorting by:', sortKey);
                });
            });
        });
        
        // Bulk actions
        const bulkActionForms = document.querySelectorAll('.bulk-actions');
        bulkActionForms.forEach(form => {
            const selectAll = form.querySelector('.select-all');
            const checkboxes = form.querySelectorAll('.item-checkbox');
            const actionButton = form.querySelector('.bulk-action-btn');
            
            if (selectAll) {
                selectAll.addEventListener('change', () => {
                    checkboxes.forEach(checkbox => {
                        checkbox.checked = selectAll.checked;
                    });
                    updateBulkActionButton();
                });
            }
            
            checkboxes.forEach(checkbox => {
                checkbox.addEventListener('change', updateBulkActionButton);
            });
            
            function updateBulkActionButton() {
                const checkedCount = form.querySelectorAll('.item-checkbox:checked').length;
                if (actionButton) {
                    actionButton.disabled = checkedCount === 0;
                    actionButton.textContent = checkedCount > 0 ? 
                        `Actions (${checkedCount} selected)` : 'Select items';
                }
            }
        });
        
        // Real-time search
        const searchInputs = document.querySelectorAll('.live-search');
        searchInputs.forEach(input => {
            input.addEventListener('input', ArtistPortfolio.debounce((e) => {
                const query = e.target.value;
                const targetTable = document.querySelector(e.target.getAttribute('data-target'));
                
                if (targetTable) {
                    const rows = targetTable.querySelectorAll('tbody tr');
                    rows.forEach(row => {
                        const text = row.textContent.toLowerCase();
                        row.style.display = text.includes(query.toLowerCase()) ? '' : 'none';
                    });
                }
            }, 300));
        });
        
        // Statistics animation
        const animateNumbers = () => {
            const numbers = document.querySelectorAll('.animate-number');
            numbers.forEach(number => {
                const target = parseInt(number.textContent.replace(/,/g, ''));
                const duration = 1000;
                const step = target / (duration / 16);
                let current = 0;
                
                const timer = setInterval(() => {
                    current += step;
                    if (current >= target) {
                        current = target;
                        clearInterval(timer);
                    }
                    number.textContent = Math.floor(current).toLocaleString();
                }, 16);
            });
        };
        
        // Run animations when page loads
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', animateNumbers);
        } else {
            animateNumbers();
        }
    </script>
    
</body>
</html>
