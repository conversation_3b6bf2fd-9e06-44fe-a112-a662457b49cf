<?php
/**
 * QR Code Redirect and Tracking Handler
 * Handles /qr/{short_code} URLs
 */

require_once 'config/config.php';
require_once 'config/database.php';
require_once 'classes/QRCodeGenerator.php';

// Get short code from URL
$short_code = $_GET['code'] ?? '';

if (empty($short_code)) {
    // No code provided - redirect to admin
    header('Location: ' . SITE_URL . '/admin/qr_codes.php');
    exit;
}

try {
    $qr_generator = new QRCodeGenerator();
    
    // Get QR code data
    $qr_code = $qr_generator->getQRCodeByShortCode($short_code);
    
    if (!$qr_code) {
        // QR code not found
        http_response_code(404);
        include 'includes/header.php';
        ?>
        <div class="container">
            <div class="section">
                <div class="has-text-centered">
                    <span class="icon is-large has-text-danger">
                        <i class="fas fa-exclamation-triangle fa-3x"></i>
                    </span>
                    <h1 class="title is-3 mt-4">QR Code Not Found</h1>
                    <p class="subtitle">The QR code you're looking for doesn't exist or has been deactivated.</p>
                    <a href="<?php echo SITE_URL; ?>/admin/qr_codes.php" class="button is-primary">
                        <span class="icon"><i class="fas fa-qrcode"></i></span>
                        <span>Manage QR Codes</span>
                    </a>
                </div>
            </div>
        </div>
        <?php
        include 'includes/footer.php';
        exit;
    }
    
    if (empty($qr_code['target_url'])) {
        // QR code not mapped to any URL
        include 'includes/header.php';
        ?>
        <div class="container">
            <div class="section">
                <div class="has-text-centered">
                    <span class="icon is-large has-text-warning">
                        <i class="fas fa-link fa-3x"></i>
                    </span>
                    <h1 class="title is-3 mt-4">QR Code Not Configured</h1>
                    <p class="subtitle">This QR code hasn't been mapped to a destination URL yet.</p>
                    <div class="content">
                        <p><strong>QR Code:</strong> <?php echo htmlspecialchars($qr_code['title']); ?></p>
                        <p><strong>Short URL:</strong> <?php echo htmlspecialchars($qr_code['short_url']); ?></p>
                    </div>
                    <a href="<?php echo SITE_URL; ?>/admin/qr_codes.php?action=edit&id=<?php echo $qr_code['id']; ?>" class="button is-primary">
                        <span class="icon"><i class="fas fa-edit"></i></span>
                        <span>Configure This QR Code</span>
                    </a>
                </div>
            </div>
        </div>
        <?php
        include 'includes/footer.php';
        exit;
    }
    
    // Track the scan
    $qr_generator->trackScan($short_code);
    
    // Redirect to target URL
    header('Location: ' . $qr_code['target_url'], true, 302);
    exit;
    
} catch (Exception $e) {
    // Error occurred
    error_log("QR redirect error: " . $e->getMessage());
    
    http_response_code(500);
    include 'includes/header.php';
    ?>
    <div class="container">
        <div class="section">
            <div class="has-text-centered">
                <span class="icon is-large has-text-danger">
                    <i class="fas fa-exclamation-circle fa-3x"></i>
                </span>
                <h1 class="title is-3 mt-4">Something Went Wrong</h1>
                <p class="subtitle">We encountered an error while processing your QR code.</p>
                <a href="<?php echo SITE_URL; ?>/admin/qr_codes.php" class="button is-primary">
                    <span class="icon"><i class="fas fa-home"></i></span>
                    <span>Go to QR Management</span>
                </a>
            </div>
        </div>
    </div>
    <?php
    include 'includes/footer.php';
}
?>
