<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    
    <!-- SEO Meta Tags -->
    <title><?php echo isset($page_title) ? htmlspecialchars($page_title) . ' - ' . SITE_NAME : SITE_NAME; ?></title>
    <meta name="description" content="<?php echo isset($page_description) ? htmlspecialchars($page_description) : 'Artist Portfolio Management System'; ?>">
    <meta name="keywords" content="<?php echo isset($page_keywords) ? htmlspecialchars($page_keywords) : 'artist, portfolio, gallery, art'; ?>">
    <meta name="author" content="<?php echo SITE_NAME; ?>">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="<?php echo isset($page_title) ? htmlspecialchars($page_title) : SITE_NAME; ?>">
    <meta property="og:description" content="<?php echo isset($page_description) ? htmlspecialchars($page_description) : 'Artist Portfolio Management System'; ?>">
    <meta property="og:type" content="website">
    <meta property="og:url" content="<?php echo SITE_URL . $_SERVER['REQUEST_URI']; ?>">
    <?php if (isset($page_image)): ?>
    <meta property="og:image" content="<?php echo SITE_URL . '/' . htmlspecialchars($page_image); ?>">
    <?php endif; ?>
    
    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="<?php echo isset($page_title) ? htmlspecialchars($page_title) : SITE_NAME; ?>">
    <meta name="twitter:description" content="<?php echo isset($page_description) ? htmlspecialchars($page_description) : 'Artist Portfolio Management System'; ?>">
    <?php if (isset($page_image)): ?>
    <meta name="twitter:image" content="<?php echo SITE_URL . '/' . htmlspecialchars($page_image); ?>">
    <?php endif; ?>
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="<?php echo SITE_URL; ?>/assets/images/favicon.ico">
    <link rel="apple-touch-icon" href="<?php echo SITE_URL; ?>/assets/images/apple-touch-icon.png">
    
    <!-- CSS -->
    <link rel="stylesheet" href="<?php echo SITE_URL; ?>/assets/css/style.css">
    
    <!-- Additional CSS -->
    <?php if (isset($additional_css)): ?>
        <?php foreach ($additional_css as $css): ?>
            <link rel="stylesheet" href="<?php echo htmlspecialchars($css); ?>">
        <?php endforeach; ?>
    <?php endif; ?>
    
    <!-- Preload critical resources -->
    <link rel="preload" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css" as="style">
    <link rel="preload" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" as="style">
    
    <!-- Schema.org structured data -->
    <?php if (isset($schema_data)): ?>
    <script type="application/ld+json">
    <?php echo json_encode($schema_data, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES); ?>
    </script>
    <?php endif; ?>
</head>
<body class="<?php echo isset($body_class) ? htmlspecialchars($body_class) : ''; ?>">
    
    <!-- Skip to main content for accessibility -->
    <a href="#main-content" class="skip-link">Skip to main content</a>
    
    <!-- Navigation -->
    <?php if (!isset($hide_navigation) || !$hide_navigation): ?>
    <nav class="navbar is-primary" role="navigation" aria-label="main navigation">
        <div class="container">
            <div class="navbar-brand">
                <a class="navbar-item" href="<?php echo SITE_URL; ?>">
                    <strong><?php echo SITE_NAME; ?></strong>
                </a>
                
                <a role="button" class="navbar-burger" aria-label="menu" aria-expanded="false" data-target="navbarMenu">
                    <span aria-hidden="true"></span>
                    <span aria-hidden="true"></span>
                    <span aria-hidden="true"></span>
                </a>
            </div>
            
            <div id="navbarMenu" class="navbar-menu">
                <div class="navbar-start">
                    <a class="navbar-item" href="<?php echo SITE_URL; ?>">
                        <span class="icon"><i class="fas fa-home"></i></span>
                        <span>Home</span>
                    </a>
                    
                    <?php if (isset($_SESSION['admin_logged_in']) && $_SESSION['admin_logged_in']): ?>
                    <a class="navbar-item" href="<?php echo SITE_URL; ?>/admin">
                        <span class="icon"><i class="fas fa-tachometer-alt"></i></span>
                        <span>Dashboard</span>
                    </a>
                    <?php endif; ?>
                </div>
                
                <div class="navbar-end">
                    <!-- Language Selector -->
                    <?php if (isset($language)): ?>
                    <div class="navbar-item">
                        <?php echo $language->generateLanguageSelector(); ?>
                    </div>
                    <?php endif; ?>

                    <?php if (isset($_SESSION['admin_logged_in']) && $_SESSION['admin_logged_in']): ?>
                    <div class="navbar-item has-dropdown is-hoverable">
                        <a class="navbar-link">
                            <span class="icon"><i class="fas fa-user"></i></span>
                            <span>Admin</span>
                        </a>

                        <div class="navbar-dropdown">
                            <a class="navbar-item" href="<?php echo SITE_URL; ?>/admin/profile.php">
                                <span class="icon"><i class="fas fa-user-cog"></i></span>
                                <span>Profile</span>
                            </a>
                            <a class="navbar-item" href="<?php echo SITE_URL; ?>/admin/settings.php">
                                <span class="icon"><i class="fas fa-cog"></i></span>
                                <span>Settings</span>
                            </a>
                            <hr class="navbar-divider">
                            <a class="navbar-item" href="<?php echo SITE_URL; ?>/admin/logout.php">
                                <span class="icon"><i class="fas fa-sign-out-alt"></i></span>
                                <span>Logout</span>
                            </a>
                        </div>
                    </div>
                    <?php else: ?>
                    <div class="navbar-item">
                        <div class="buttons">
                            <a class="button is-light" href="<?php echo SITE_URL; ?>/admin/login.php">
                                <span class="icon"><i class="fas fa-sign-in-alt"></i></span>
                                <span>Admin Login</span>
                            </a>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </nav>
    <?php endif; ?>
    
    <!-- Main Content -->
    <main id="main-content" class="<?php echo isset($main_class) ? htmlspecialchars($main_class) : 'section'; ?>">
        
        <!-- Flash Messages -->
        <?php if (isset($_SESSION['flash_message'])): ?>
        <div class="container">
            <div class="notification is-<?php echo htmlspecialchars($_SESSION['flash_type'] ?? 'info'); ?> is-light">
                <button class="delete"></button>
                <?php echo htmlspecialchars($_SESSION['flash_message']); ?>
            </div>
        </div>
        <?php 
        unset($_SESSION['flash_message'], $_SESSION['flash_type']); 
        endif; 
        ?>
