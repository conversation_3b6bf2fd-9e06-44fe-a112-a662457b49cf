<?php
require_once 'config/config.php';
require_once 'config/database.php';

echo "<h2>Direct Delete Test</h2>";

try {
    $db = Database::getInstance();
    
    // Get test data
    $stmt = $db->query("SELECT id, name FROM artists LIMIT 1");
    $test_artist = $stmt->fetch();
    
    echo "<h3>Test Artist</h3>";
    if ($test_artist) {
        echo "<p>Artist: " . htmlspecialchars($test_artist['name']) . " (ID: " . $test_artist['id'] . ")</p>";
        
        // Show the exact URLs that would be called
        $delete_url = "admin/artists.php?action=delete&id=" . $test_artist['id'];
        echo "<p>Delete URL: <code>" . htmlspecialchars($delete_url) . "</code></p>";
        
        echo "<h3>Direct URL Tests</h3>";
        echo "<p>Click these links to test the delete URLs directly:</p>";
        
        echo "<p><a href='" . $delete_url . "' target='_blank' style='background: red; color: white; padding: 10px; text-decoration: none;'>
                🗑️ Test Delete URL Directly
              </a></p>";
        
        echo "<h3>Check What Happens</h3>";
        echo "<ol>";
        echo "<li>Click the link above</li>";
        echo "<li>See what page loads (error, success, or white page)</li>";
        echo "<li>Check if the artist is actually deleted from database</li>";
        echo "<li>Look at browser network tab to see the HTTP response</li>";
        echo "</ol>";
        
        // Test if the action parameter is being received
        echo "<h3>Parameter Test</h3>";
        echo "<p>Current page parameters:</p>";
        echo "<ul>";
        echo "<li>action: " . ($_GET['action'] ?? 'not set') . "</li>";
        echo "<li>id: " . ($_GET['id'] ?? 'not set') . "</li>";
        echo "</ul>";
        
        // Test the delete logic directly
        if (isset($_GET['test_delete']) && $_GET['test_delete'] == $test_artist['id']) {
            echo "<h3>🔥 DIRECT DELETE TEST EXECUTION</h3>";
            
            try {
                echo "<p>Starting delete test for artist ID: " . $test_artist['id'] . "</p>";
                
                $db->beginTransaction();
                
                // Count before delete
                $stmt = $db->query("SELECT COUNT(*) as count FROM artists WHERE id = ?", [$test_artist['id']]);
                $before_count = $stmt->fetch()['count'];
                echo "<p>Artists with this ID before delete: " . $before_count . "</p>";
                
                // Perform delete
                $stmt = $db->query("DELETE FROM artists WHERE id = ?", [$test_artist['id']]);
                $affected_rows = $stmt->rowCount();
                echo "<p>Delete query affected rows: " . $affected_rows . "</p>";
                
                // Count after delete
                $stmt = $db->query("SELECT COUNT(*) as count FROM artists WHERE id = ?", [$test_artist['id']]);
                $after_count = $stmt->fetch()['count'];
                echo "<p>Artists with this ID after delete: " . $after_count . "</p>";
                
                if ($affected_rows > 0) {
                    echo "<p style='color: green;'>✅ DELETE SUCCESSFUL!</p>";
                    $db->commit();
                } else {
                    echo "<p style='color: red;'>❌ No rows were deleted</p>";
                    $db->rollback();
                }
                
            } catch (Exception $e) {
                $db->rollback();
                echo "<p style='color: red;'>❌ Delete failed: " . htmlspecialchars($e->getMessage()) . "</p>";
            }
        } else {
            echo "<p><a href='?test_delete=" . $test_artist['id'] . "' style='background: orange; color: white; padding: 10px; text-decoration: none;'>
                    🧪 Run Direct Delete Test
                  </a></p>";
        }
        
    } else {
        echo "<p>No artists found for testing</p>";
    }
    
    // Check current artists count
    $stmt = $db->query("SELECT COUNT(*) as count FROM artists");
    $total_artists = $stmt->fetch()['count'];
    echo "<h3>Current Database State</h3>";
    echo "<p>Total artists in database: " . $total_artists . "</p>";
    
    // Show recent artists
    $stmt = $db->query("SELECT id, name FROM artists ORDER BY created_at DESC LIMIT 5");
    $recent_artists = $stmt->fetchAll();
    
    echo "<h4>Recent Artists:</h4>";
    echo "<ul>";
    foreach ($recent_artists as $artist) {
        echo "<li>ID: " . $artist['id'] . " - " . htmlspecialchars($artist['name']) . "</li>";
    }
    echo "</ul>";
    
    echo "<h3>Navigation</h3>";
    echo "<p><a href='admin/index.php'>Dashboard</a> | <a href='admin/artists.php'>Artists Management</a></p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
}
?>
