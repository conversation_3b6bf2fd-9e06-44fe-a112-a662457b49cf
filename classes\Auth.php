<?php
/**
 * Authentication Class
 * Handles user login, logout, and session management
 */

class Auth {
    private $db;
    
    public function __construct() {
        $this->db = Database::getInstance();
    }
    
    /**
     * Authenticate user login
     */
    public function login($username, $password) {
        try {
            // Get user from database
            $stmt = $this->db->query(
                "SELECT id, username, email, password_hash, full_name, role, is_active, last_login 
                 FROM admin_users 
                 WHERE (username = ? OR email = ?) AND is_active = 1",
                [$username, $username]
            );
            
            $user = $stmt->fetch();
            
            if (!$user) {
                return ['success' => false, 'message' => 'Invalid username or password'];
            }
            
            // Verify password
            if (!password_verify($password, $user['password_hash'])) {
                return ['success' => false, 'message' => 'Invalid username or password'];
            }
            
            // Update last login
            $this->updateLastLogin($user['id']);
            
            // Set session
            $this->setUserSession($user);
            
            return ['success' => true, 'message' => 'Login successful', 'user' => $user];
            
        } catch (Exception $e) {
            error_log("Login error: " . $e->getMessage());
            return ['success' => false, 'message' => 'Login failed. Please try again.'];
        }
    }
    
    /**
     * Logout user
     */
    public function logout() {
        // Destroy session
        session_unset();
        session_destroy();
        
        // Start new session for flash messages
        session_start();
        session_regenerate_id(true);
        
        return ['success' => true, 'message' => 'Logged out successfully'];
    }
    
    /**
     * Check if user is logged in
     */
    public function isLoggedIn() {
        return isset($_SESSION['admin_logged_in']) && $_SESSION['admin_logged_in'] === true;
    }
    
    /**
     * Get current user data
     */
    public function getCurrentUser() {
        if (!$this->isLoggedIn()) {
            return null;
        }
        
        return [
            'id' => $_SESSION['admin_id'] ?? null,
            'username' => $_SESSION['admin_username'] ?? null,
            'email' => $_SESSION['admin_email'] ?? null,
            'full_name' => $_SESSION['admin_full_name'] ?? null,
            'role' => $_SESSION['admin_role'] ?? null
        ];
    }
    
    /**
     * Check if user has specific role
     */
    public function hasRole($role) {
        $user = $this->getCurrentUser();
        return $user && $user['role'] === $role;
    }
    
    /**
     * Require login (redirect if not logged in)
     */
    public function requireLogin($redirect_url = null) {
        if (!$this->isLoggedIn()) {
            $redirect_url = $redirect_url ?: SITE_URL . '/admin/login.php';
            header("Location: $redirect_url");
            exit;
        }
        
        // Check session timeout
        if ($this->isSessionExpired()) {
            $this->logout();
            $_SESSION['flash_message'] = 'Your session has expired. Please login again.';
            $_SESSION['flash_type'] = 'warning';
            header("Location: " . SITE_URL . "/admin/login.php");
            exit;
        }
        
        // Update last activity
        $_SESSION['last_activity'] = time();
    }
    
    /**
     * Check if session is expired
     */
    private function isSessionExpired() {
        if (!isset($_SESSION['last_activity'])) {
            return true;
        }
        
        return (time() - $_SESSION['last_activity']) > SESSION_TIMEOUT;
    }
    
    /**
     * Set user session data
     */
    private function setUserSession($user) {
        session_regenerate_id(true);
        
        $_SESSION['admin_logged_in'] = true;
        $_SESSION['admin_id'] = $user['id'];
        $_SESSION['admin_username'] = $user['username'];
        $_SESSION['admin_email'] = $user['email'];
        $_SESSION['admin_full_name'] = $user['full_name'];
        $_SESSION['admin_role'] = $user['role'];
        $_SESSION['login_time'] = time();
        $_SESSION['last_activity'] = time();
    }
    
    /**
     * Update user's last login time
     */
    private function updateLastLogin($user_id) {
        try {
            $this->db->query(
                "UPDATE admin_users SET last_login = NOW() WHERE id = ?",
                [$user_id]
            );
        } catch (Exception $e) {
            error_log("Failed to update last login: " . $e->getMessage());
        }
    }
    
    /**
     * Change user password
     */
    public function changePassword($user_id, $current_password, $new_password) {
        try {
            // Get current password hash
            $stmt = $this->db->query(
                "SELECT password_hash FROM admin_users WHERE id = ?",
                [$user_id]
            );
            
            $user = $stmt->fetch();
            if (!$user) {
                return ['success' => false, 'message' => 'User not found'];
            }
            
            // Verify current password
            if (!password_verify($current_password, $user['password_hash'])) {
                return ['success' => false, 'message' => 'Current password is incorrect'];
            }
            
            // Validate new password
            if (strlen($new_password) < 8) {
                return ['success' => false, 'message' => 'New password must be at least 8 characters long'];
            }
            
            // Update password
            $new_hash = password_hash($new_password, PASSWORD_DEFAULT);
            $this->db->query(
                "UPDATE admin_users SET password_hash = ?, updated_at = NOW() WHERE id = ?",
                [$new_hash, $user_id]
            );
            
            return ['success' => true, 'message' => 'Password changed successfully'];
            
        } catch (Exception $e) {
            error_log("Password change error: " . $e->getMessage());
            return ['success' => false, 'message' => 'Failed to change password'];
        }
    }
    
    /**
     * Generate CSRF token
     */
    public function generateCSRFToken() {
        if (!isset($_SESSION['csrf_token'])) {
            $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
        }
        return $_SESSION['csrf_token'];
    }
    
    /**
     * Verify CSRF token
     */
    public function verifyCSRFToken($token) {
        return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
    }
    
    /**
     * Create new admin user
     */
    public function createUser($username, $email, $password, $full_name, $role = 'admin') {
        try {
            // Check if username or email already exists
            $stmt = $this->db->query(
                "SELECT id FROM admin_users WHERE username = ? OR email = ?",
                [$username, $email]
            );
            
            if ($stmt->fetch()) {
                return ['success' => false, 'message' => 'Username or email already exists'];
            }
            
            // Validate password
            if (strlen($password) < 8) {
                return ['success' => false, 'message' => 'Password must be at least 8 characters long'];
            }
            
            // Create user
            $password_hash = password_hash($password, PASSWORD_DEFAULT);
            $stmt = $this->db->query(
                "INSERT INTO admin_users (username, email, password_hash, full_name, role) VALUES (?, ?, ?, ?, ?)",
                [$username, $email, $password_hash, $full_name, $role]
            );
            
            return ['success' => true, 'message' => 'User created successfully', 'user_id' => $this->db->lastInsertId()];
            
        } catch (Exception $e) {
            error_log("User creation error: " . $e->getMessage());
            return ['success' => false, 'message' => 'Failed to create user'];
        }
    }
    
    /**
     * Get user by ID
     */
    public function getUserById($user_id) {
        try {
            $stmt = $this->db->query(
                "SELECT id, username, email, full_name, role, is_active, last_login, created_at 
                 FROM admin_users WHERE id = ?",
                [$user_id]
            );
            
            return $stmt->fetch();
        } catch (Exception $e) {
            error_log("Get user error: " . $e->getMessage());
            return null;
        }
    }
}
?>
