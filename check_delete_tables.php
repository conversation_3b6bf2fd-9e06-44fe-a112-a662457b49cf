<?php
require_once 'config/config.php';
require_once 'config/database.php';

echo "<h2>Check Delete-Related Tables</h2>";

try {
    $db = Database::getInstance();
    
    // Check which tables exist
    $tables_to_check = [
        'artists',
        'artist_translations', 
        'artist_galleries',
        'qr_codes',
        'qr_artist_mappings'
    ];
    
    echo "<h3>Table Existence Check</h3>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>Table</th><th>Exists</th><th>Row Count</th></tr>";
    
    foreach ($tables_to_check as $table) {
        try {
            $stmt = $db->query("SELECT COUNT(*) as count FROM $table");
            $result = $stmt->fetch();
            $count = $result['count'];
            echo "<tr>";
            echo "<td>$table</td>";
            echo "<td style='color: green;'>✅ Yes</td>";
            echo "<td>$count rows</td>";
            echo "</tr>";
        } catch (Exception $e) {
            echo "<tr>";
            echo "<td>$table</td>";
            echo "<td style='color: red;'>❌ No</td>";
            echo "<td>Error: " . htmlspecialchars($e->getMessage()) . "</td>";
            echo "</tr>";
        }
    }
    echo "</table>";
    
    // Test delete on a copy (safe test)
    echo "<h3>Safe Delete Test</h3>";
    
    // Get a test artist
    $stmt = $db->query("SELECT id, name FROM artists LIMIT 1");
    $test_artist = $stmt->fetch();
    
    if ($test_artist) {
        echo "<p>Test Artist: " . htmlspecialchars($test_artist['name']) . " (ID: " . $test_artist['id'] . ")</p>";
        
        echo "<h4>Test Delete Queries (DRY RUN)</h4>";
        
        $queries = [
            "DELETE FROM artist_translations WHERE artist_id = " . $test_artist['id'],
            "DELETE FROM artist_galleries WHERE artist_id = " . $test_artist['id'], 
            "DELETE FROM qr_artist_mappings WHERE artist_id = " . $test_artist['id'],
            "DELETE FROM artists WHERE id = " . $test_artist['id']
        ];
        
        foreach ($queries as $query) {
            try {
                // Use EXPLAIN to test query without executing
                $test_query = "EXPLAIN " . $query;
                $stmt = $db->query($test_query);
                echo "<p style='color: green;'>✅ Query OK: " . htmlspecialchars($query) . "</p>";
            } catch (Exception $e) {
                echo "<p style='color: red;'>❌ Query Error: " . htmlspecialchars($query) . "</p>";
                echo "<p style='color: red; margin-left: 20px;'>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
            }
        }
        
        echo "<h4>Actual Delete Test</h4>";
        echo "<p><strong>WARNING:</strong> The buttons below will actually delete the test artist!</p>";
        echo '<a href="admin/artists.php?action=delete&id=' . $test_artist['id'] . '" 
                style="background: red; color: white; padding: 10px; text-decoration: none; border-radius: 5px;"
                onclick="return confirm(\'Really delete ' . htmlspecialchars($test_artist['name']) . '?\')">
                🗑️ Actually Delete Test Artist
              </a>';
    } else {
        echo "<p>No artists found for testing</p>";
    }
    
    // Test QR code
    $stmt = $db->query("SELECT id, title FROM qr_codes LIMIT 1");
    $test_qr = $stmt->fetch();
    
    if ($test_qr) {
        echo "<h4>QR Code Delete Test</h4>";
        echo "<p>Test QR: " . htmlspecialchars($test_qr['title']) . " (ID: " . $test_qr['id'] . ")</p>";
        echo '<a href="admin/qr_codes.php?action=delete&id=' . $test_qr['id'] . '" 
                style="background: orange; color: white; padding: 10px; text-decoration: none; border-radius: 5px;"
                onclick="return confirm(\'Really delete ' . htmlspecialchars($test_qr['title']) . '?\')">
                🗑️ Actually Delete Test QR Code
              </a>';
    }
    
    echo "<h3>Check Error Logs</h3>";
    echo "<p>After testing delete, check the PHP error log for DEBUG messages:</p>";
    echo "<ul>";
    echo "<li>Look for messages starting with 'DEBUG:'</li>";
    echo "<li>Check for any SQL errors or exceptions</li>";
    echo "<li>Verify transaction commit/rollback messages</li>";
    echo "</ul>";
    
    echo "<h3>Navigation</h3>";
    echo "<p><a href='admin/index.php'>Dashboard</a> | <a href='admin/qr_codes.php'>QR Codes</a></p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
}
?>
