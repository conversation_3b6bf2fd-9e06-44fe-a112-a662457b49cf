<?php
/**
 * Create Missing Tables Script
 * Run this to create missing database tables
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Create Missing Tables</h1>";

// Include config
require_once 'config/config.php';
require_once 'config/database.php';

try {
    $db = Database::getInstance();
    echo "✅ Database connected<br>";
    
    // Create users table
    echo "<h2>Creating users table...</h2>";
    $sql = "CREATE TABLE IF NOT EXISTS `users` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `username` varchar(50) NOT NULL,
        `email` varchar(100) NOT NULL,
        `password` varchar(255) NOT NULL,
        `role` enum('admin','user') NOT NULL DEFAULT 'user',
        `is_active` tinyint(1) NOT NULL DEFAULT 1,
        `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
        `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        UNIQUE KEY `username` (`username`),
        UNIQUE KEY `email` (`email`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";
    
    $db->query($sql);
    echo "✅ Users table created<br>";
    
    // Create admin user
    echo "<h2>Creating admin user...</h2>";
    $admin_password = password_hash('admin123', PASSWORD_DEFAULT);
    $sql = "INSERT IGNORE INTO `users` (`username`, `email`, `password`, `role`, `is_active`) VALUES
            ('admin', '<EMAIL>', ?, 'admin', 1)";
    $db->query($sql, [$admin_password]);
    echo "✅ Admin user created (username: admin, password: admin123)<br>";
    
    // Create artists table
    echo "<h2>Creating artists table...</h2>";
    $sql = "CREATE TABLE IF NOT EXISTS `artists` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `name` varchar(100) NOT NULL,
        `slug` varchar(100) NOT NULL,
        `bio` text,
        `avatar_image` varchar(255) DEFAULT NULL,
        `is_active` tinyint(1) NOT NULL DEFAULT 1,
        `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
        `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        UNIQUE KEY `slug` (`slug`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";
    
    $db->query($sql);
    echo "✅ Artists table created<br>";
    
    // Create artist_galleries table
    echo "<h2>Creating artist_galleries table...</h2>";
    $sql = "CREATE TABLE IF NOT EXISTS `artist_galleries` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `artist_id` int(11) NOT NULL,
        `title` varchar(200) DEFAULT NULL,
        `description` text,
        `image_path` varchar(255) NOT NULL,
        `sort_order` int(11) DEFAULT 0,
        `is_active` tinyint(1) NOT NULL DEFAULT 1,
        `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        KEY `artist_id` (`artist_id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";
    
    $db->query($sql);
    echo "✅ Artist galleries table created<br>";
    
    // Create artist_social_links table
    echo "<h2>Creating artist_social_links table...</h2>";
    $sql = "CREATE TABLE IF NOT EXISTS `artist_social_links` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `artist_id` int(11) NOT NULL,
        `platform` varchar(50) NOT NULL,
        `url` varchar(255) NOT NULL,
        `is_active` tinyint(1) NOT NULL DEFAULT 1,
        `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        KEY `artist_id` (`artist_id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";
    
    $db->query($sql);
    echo "✅ Artist social links table created<br>";
    
    echo "<h2>✅ All tables created successfully!</h2>";
    echo "<p><strong>Admin Login Credentials:</strong></p>";
    echo "<ul>";
    echo "<li>Username: <strong>admin</strong></li>";
    echo "<li>Password: <strong>admin123</strong></li>";
    echo "</ul>";
    
    echo "<p><a href='test_connection.php'>Test Connection Again</a></p>";
    echo "<p><a href='admin/login.php'>Go to Admin Login</a></p>";
    
    echo "<p><strong>🔒 Delete this file after use!</strong></p>";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "<br>";
    echo "Stack trace: " . $e->getTraceAsString() . "<br>";
}
?>
