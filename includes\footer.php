    </main>

    <!-- Footer -->
    <?php if (!isset($hide_footer) || !$hide_footer): ?>
    <footer class="footer">
        <div class="container">
            <div class="content has-text-centered">
                <div class="columns">
                    <div class="column">
                        <h5 class="title is-5"><?php echo SITE_NAME; ?></h5>
                        <p class="subtitle is-6">Artist Portfolio Management System</p>
                        <p>Empowering artists to showcase their work beautifully.</p>
                    </div>

                    <div class="column">
                        <h6 class="title is-6">Quick Links</h6>
                        <ul class="footer-links">
                            <li><a href="<?php echo SITE_URL; ?>">Home</a></li>
                            <?php if (isset($_SESSION['admin_logged_in']) && $_SESSION['admin_logged_in']): ?>
                            <li><a href="<?php echo SITE_URL; ?>/admin">Dashboard</a></li>
                            <li><a href="<?php echo SITE_URL; ?>/admin/artists.php">Manage Artists</a></li>
                            <li><a href="<?php echo SITE_URL; ?>/admin/analytics.php">Analytics</a></li>
                            <?php else: ?>
                            <li><a href="<?php echo SITE_URL; ?>/admin/login.php">Admin Login</a></li>
                            <?php endif; ?>
                        </ul>
                    </div>

                    <div class="column">
                        <h6 class="title is-6">Features</h6>
                        <ul class="footer-links">
                            <li>Mobile-Responsive Design</li>
                            <li>QR Code Generation</li>
                            <li>Analytics Tracking</li>
                            <li>Social Media Integration</li>
                            <li>Gallery Management</li>
                        </ul>
                    </div>

                    <div class="column">
                        <h6 class="title is-6">Contact</h6>
                        <p>
                            <span class="icon"><i class="fas fa-envelope"></i></span>
                            <a href="mailto:<?php echo ADMIN_EMAIL; ?>"><?php echo ADMIN_EMAIL; ?></a>
                        </p>
                        <p>
                            <span class="icon"><i class="fas fa-globe"></i></span>
                            <a href="<?php echo SITE_URL; ?>" target="_blank"><?php echo str_replace(['http://', 'https://'], '', SITE_URL); ?></a>
                        </p>
                    </div>
                </div>

                <hr>

                <div class="level">
                    <div class="level-left">
                        <div class="level-item">
                            <p>&copy; <?php echo date('Y'); ?> <?php echo SITE_NAME; ?>. All rights reserved.</p>
                        </div>
                    </div>

                    <div class="level-right">
                        <div class="level-item">
                            <p>
                                <small>
                                    Powered by
                                    <a href="https://bulma.io" target="_blank" rel="noopener">Bulma</a> &
                                    <a href="https://fontawesome.com" target="_blank" rel="noopener">Font Awesome</a>
                                </small>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </footer>
    <?php endif; ?>

    <!-- JavaScript -->
    <script src="<?php echo SITE_URL; ?>/assets/js/app.js"></script>

    <!-- Additional JavaScript -->
    <?php if (isset($additional_js)): ?>
        <?php foreach ($additional_js as $js): ?>
            <script src="<?php echo htmlspecialchars($js); ?>"></script>
        <?php endforeach; ?>
    <?php endif; ?>

    <!-- Inline JavaScript -->
    <?php if (isset($inline_js)): ?>
    <script>
        <?php echo $inline_js; ?>
    </script>
    <?php endif; ?>

    <!-- Analytics (if configured) -->
    <?php if (defined('GOOGLE_ANALYTICS_ID') && GOOGLE_ANALYTICS_ID): ?>
    <!-- Google Analytics -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=<?php echo GOOGLE_ANALYTICS_ID; ?>"></script>
    <script>
        window.dataLayer = window.dataLayer || [];
        function gtag(){dataLayer.push(arguments);}
        gtag('js', new Date());
        gtag('config', '<?php echo GOOGLE_ANALYTICS_ID; ?>');
    </script>
    <?php endif; ?>

    <!-- Service Worker Registration (for PWA features) -->
    <script>
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', function() {
                navigator.serviceWorker.register('<?php echo SITE_URL; ?>/sw.js')
                    .then(function(registration) {
                        console.log('ServiceWorker registration successful');
                    }, function(err) {
                        console.log('ServiceWorker registration failed: ', err);
                    });
            });
        }
    </script>

    <!-- Accessibility enhancements -->
    <script>
        // Skip link functionality
        document.querySelector('.skip-link')?.addEventListener('click', function(e) {
            e.preventDefault();
            document.getElementById('main-content').focus();
        });

        // Keyboard navigation for dropdowns
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                // Close all open dropdowns and modals
                document.querySelectorAll('.dropdown.is-active').forEach(dropdown => {
                    dropdown.classList.remove('is-active');
                });
                document.querySelectorAll('.modal.is-active').forEach(modal => {
                    modal.classList.remove('is-active');
                });
            }
        });

        // Auto-hide flash messages
        document.querySelectorAll('.notification .delete').forEach(deleteBtn => {
            deleteBtn.addEventListener('click', function() {
                this.parentElement.style.display = 'none';
            });
        });

        // Auto-hide notifications after 5 seconds
        setTimeout(() => {
            document.querySelectorAll('.notification.is-light').forEach(notification => {
                notification.style.opacity = '0';
                setTimeout(() => {
                    notification.style.display = 'none';
                }, 300);
            });
        }, 5000);
    </script>

    <!-- Performance monitoring -->
    <script>
        // Basic performance monitoring
        window.addEventListener('load', function() {
            if (window.performance && window.performance.timing) {
                const loadTime = window.performance.timing.loadEventEnd - window.performance.timing.navigationStart;
                console.log('Page load time:', loadTime + 'ms');

                // Send to analytics if configured
                if (typeof gtag !== 'undefined') {
                    gtag('event', 'timing_complete', {
                        'name': 'load',
                        'value': loadTime
                    });
                }
            }
        });
    </script>

</body>
</html>
