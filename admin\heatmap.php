<?php
/**
 * Heatmap Analytics Dashboard
 * Visualizes user interaction heatmaps
 */

require_once '../config/config.php';
require_once '../config/database.php';
require_once 'includes/auth_check.php';

$db = Database::getInstance();

// Get date range and filters
$date_from = $_GET['from'] ?? date('Y-m-d', strtotime('-7 days'));
$date_to = $_GET['to'] ?? date('Y-m-d');
$artist_id = $_GET['artist_id'] ?? null;

// Page configuration
$page_title = 'Heatmap Analytics';
$page_description = 'Visual heatmap analytics showing user interaction patterns';

include 'includes/admin_header.php';

try {
    // Get heatmap data
    $where_clause = "WHERE timestamp BETWEEN ? AND ?";
    $params = [$date_from . ' 00:00:00', $date_to . ' 23:59:59'];
    
    if ($artist_id) {
        $where_clause .= " AND artist_id = ?";
        $params[] = $artist_id;
    }
    
    $stmt = $db->query("SELECT 
                           page_url, 
                           element_selector, 
                           interaction_type,
                           x_coordinate, 
                           y_coordinate, 
                           viewport_width, 
                           viewport_height,
                           COUNT(*) as interaction_count
                       FROM analytics_heatmap_data 
                       {$where_clause}
                       GROUP BY page_url, element_selector, interaction_type, x_coordinate, y_coordinate
                       ORDER BY interaction_count DESC", $params);
    $heatmap_data = $stmt->fetchAll() ?: [];
    
    // Get available artists for filter
    $stmt = $db->query("SELECT DISTINCT a.id, a.name 
                       FROM artists a 
                       JOIN analytics_heatmap_data h ON a.id = h.artist_id 
                       WHERE h.timestamp BETWEEN ? AND ?
                       ORDER BY a.name", [$date_from . ' 00:00:00', $date_to . ' 23:59:59']);
    $available_artists = $stmt->fetchAll() ?: [];
    
} catch (Exception $e) {
    $error_message = $e->getMessage();
    $heatmap_data = [];
    $available_artists = [];
}
?>

<div class="container is-fluid">
    <!-- Header -->
    <div class="level mb-6">
        <div class="level-left">
            <div class="level-item">
                <div>
                    <h1 class="title is-3 mb-2 stormi-text-dark">Heatmap Analytics</h1>
                    <p class="subtitle is-5 mt-2 mb-0 has-text-grey-dark">Visual interaction patterns and user behavior</p>
                </div>
            </div>
        </div>
        <div class="level-right">
            <div class="level-item">
                <a href="analytics.php" class="button is-light">
                    <span class="icon"><i class="fas fa-arrow-left"></i></span>
                    <span>Back to Analytics</span>
                </a>
            </div>
        </div>
    </div>
    
    <!-- Filters -->
    <div class="box mb-5">
        <form method="GET" class="columns is-vcentered">
            <div class="column is-3">
                <div class="field">
                    <label class="label">From Date</label>
                    <div class="control">
                        <input type="date" class="input" name="from" value="<?php echo $date_from; ?>">
                    </div>
                </div>
            </div>
            <div class="column is-3">
                <div class="field">
                    <label class="label">To Date</label>
                    <div class="control">
                        <input type="date" class="input" name="to" value="<?php echo $date_to; ?>">
                    </div>
                </div>
            </div>
            <div class="column is-4">
                <div class="field">
                    <label class="label">Artist Filter</label>
                    <div class="control">
                        <div class="select is-fullwidth">
                            <select name="artist_id">
                                <option value="">All Artists</option>
                                <?php foreach ($available_artists as $artist): ?>
                                <option value="<?php echo $artist['id']; ?>" <?php echo $artist_id == $artist['id'] ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($artist['name']); ?>
                                </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
            <div class="column is-2">
                <div class="field">
                    <label class="label">&nbsp;</label>
                    <div class="control">
                        <button type="submit" class="button is-primary is-fullwidth">
                            <span class="icon"><i class="fas fa-filter"></i></span>
                            <span>Filter</span>
                        </button>
                    </div>
                </div>
            </div>
        </form>
    </div>
    
    <!-- Heatmap Visualization -->
    <div class="columns">
        <div class="column is-8">
            <div class="box">
                <h3 class="title is-5 stormi-text-dark mb-4">
                    <span class="icon"><i class="fas fa-fire"></i></span>
                    Interaction Heatmap
                </h3>
                
                <div id="heatmapContainer" style="position: relative; width: 100%; height: 600px; background: #f5f5f5; border: 1px solid #ddd; border-radius: 6px;">
                    <div class="has-text-centered" style="padding: 2rem;">
                        <p class="title is-6">Interactive Heatmap</p>
                        <p class="subtitle is-7">Click and hover patterns will be displayed here</p>
                        <div id="heatmapCanvas"></div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="column is-4">
            <!-- Interaction Summary -->
            <div class="box mb-5">
                <h3 class="title is-6 stormi-text-dark mb-4">
                    <span class="icon"><i class="fas fa-chart-bar"></i></span>
                    Interaction Summary
                </h3>
                
                <?php if (!empty($heatmap_data)): ?>
                <?php 
                $interaction_types = [];
                foreach ($heatmap_data as $point) {
                    $type = $point['interaction_type'];
                    if (!isset($interaction_types[$type])) {
                        $interaction_types[$type] = 0;
                    }
                    $interaction_types[$type] += $point['interaction_count'];
                }
                arsort($interaction_types);
                ?>
                
                <?php foreach ($interaction_types as $type => $count): ?>
                <div class="level is-mobile mb-2">
                    <div class="level-left">
                        <div class="level-item">
                            <span class="tag is-light">
                                <span class="icon is-small">
                                    <i class="fas fa-<?php echo $type === 'click' ? 'mouse-pointer' : ($type === 'hover' ? 'hand-pointer' : 'scroll'); ?>"></i>
                                </span>
                                <span><?php echo ucfirst($type); ?></span>
                            </span>
                        </div>
                    </div>
                    <div class="level-right">
                        <div class="level-item">
                            <strong><?php echo number_format($count); ?></strong>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
                <?php else: ?>
                <p class="has-text-grey">No interaction data available for the selected period</p>
                <?php endif; ?>
            </div>
            
            <!-- Top Elements -->
            <div class="box">
                <h3 class="title is-6 stormi-text-dark mb-4">
                    <span class="icon"><i class="fas fa-target"></i></span>
                    Most Interacted Elements
                </h3>
                
                <?php if (!empty($heatmap_data)): ?>
                <?php 
                $elements = [];
                foreach ($heatmap_data as $point) {
                    $element = $point['element_selector'] ?: 'Unknown';
                    if (!isset($elements[$element])) {
                        $elements[$element] = 0;
                    }
                    $elements[$element] += $point['interaction_count'];
                }
                arsort($elements);
                $top_elements = array_slice($elements, 0, 10, true);
                ?>
                
                <?php foreach ($top_elements as $element => $count): ?>
                <div class="level is-mobile mb-2">
                    <div class="level-left">
                        <div class="level-item">
                            <span class="tag is-light is-small">
                                <?php echo htmlspecialchars($element); ?>
                            </span>
                        </div>
                    </div>
                    <div class="level-right">
                        <div class="level-item">
                            <strong><?php echo number_format($count); ?></strong>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
                <?php else: ?>
                <p class="has-text-grey">No element data available</p>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<script>
// Heatmap data from PHP
const heatmapData = <?php echo json_encode($heatmap_data); ?>;

// Simple heatmap visualization
function renderHeatmap() {
    const container = document.getElementById('heatmapContainer');
    const canvas = document.createElement('canvas');
    canvas.width = container.offsetWidth;
    canvas.height = container.offsetHeight;
    canvas.style.position = 'absolute';
    canvas.style.top = '0';
    canvas.style.left = '0';
    
    container.innerHTML = '';
    container.appendChild(canvas);
    
    const ctx = canvas.getContext('2d');
    
    // Draw heatmap points
    heatmapData.forEach(point => {
        const x = (point.x_coordinate / point.viewport_width) * canvas.width;
        const y = (point.y_coordinate / point.viewport_height) * canvas.height;
        const intensity = Math.min(point.interaction_count / 10, 1); // Normalize intensity
        
        // Create gradient for heat effect
        const gradient = ctx.createRadialGradient(x, y, 0, x, y, 20);
        gradient.addColorStop(0, `rgba(255, 0, 0, ${intensity})`);
        gradient.addColorStop(1, 'rgba(255, 0, 0, 0)');
        
        ctx.fillStyle = gradient;
        ctx.fillRect(x - 20, y - 20, 40, 40);
    });
}

// Render heatmap when page loads
document.addEventListener('DOMContentLoaded', renderHeatmap);
window.addEventListener('resize', renderHeatmap);
</script>

<?php include 'includes/admin_footer.php'; ?>
