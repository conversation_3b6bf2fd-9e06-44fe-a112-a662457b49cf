<?php
/**
 * Admin Login Page
 */

require_once '../config/config.php';
require_once '../config/database.php';
require_once '../classes/Auth.php';

session_start();

$auth = new Auth();

// Redirect if already logged in
if ($auth->isLoggedIn()) {
    header('Location: index.php');
    exit;
}

$error = '';
$success = '';

// Handle login form submission
if ($_POST && isset($_POST['login'])) {
    $username = trim($_POST['username'] ?? '');
    $password = $_POST['password'] ?? '';
    $csrf_token = $_POST['csrf_token'] ?? '';
    
    // Verify CSRF token
    if (!$auth->verifyCSRFToken($csrf_token)) {
        $error = 'Invalid security token. Please try again.';
    } elseif (empty($username) || empty($password)) {
        $error = 'Please enter both username and password.';
    } else {
        $result = $auth->login($username, $password);
        
        if ($result['success']) {
            // Redirect to dashboard
            $redirect = $_GET['redirect'] ?? 'index.php';
            header('Location: ' . $redirect);
            exit;
        } else {
            $error = $result['message'];
        }
    }
}

// Page configuration
$page_title = 'Admin Login';
$page_description = 'Login to the Artist Portfolio Management System admin panel';
$hide_navigation = true;
$hide_footer = true;
$body_class = 'login-page';

// Generate CSRF token
$csrf_token = $auth->generateCSRFToken();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($page_title); ?> - <?php echo SITE_NAME; ?></title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="../assets/css/stormi-palette.css">
    <link rel="stylesheet" href="../assets/css/style.css">
    <style>
        .login-page {
            background: var(--bg-gradient);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .login-container {
            width: 100%;
            max-width: 400px;
            margin: 2rem;
        }
        .login-box {
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            overflow: hidden;
        }
        .login-header {
            background: var(--gradient-primary);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        .login-body {
            padding: 2rem;
        }
        .login-footer {
            background: var(--bg-secondary);
            padding: 1rem 2rem;
            text-align: center;
            border-top: 1px solid var(--border-light);
        }
        .form-control {
            border-radius: 8px;
            border: 2px solid var(--input-border);
            padding: 0.75rem 1rem;
            font-size: 1rem;
            transition: all 0.3s ease;
        }
        .form-control:focus {
            border-color: var(--input-focus);
            box-shadow: 0 0 0 3px rgba(74, 141, 183, 0.1);
        }
        .btn-login {
            background: var(--gradient-primary);
            border: none;
            border-radius: 8px;
            padding: 0.75rem 2rem;
            font-size: 1rem;
            font-weight: 600;
            color: white;
            width: 100%;
            transition: all 0.3s ease;
        }
        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px var(--shadow-medium);
        }
        .input-group {
            position: relative;
            margin-bottom: 1.5rem;
        }
        .input-icon {
            position: absolute;
            left: 1rem;
            top: 50%;
            transform: translateY(-50%);
            color: var(--text-muted);
            z-index: 2;
        }
        .input-group input {
            padding-left: 3rem;
        }
    </style>
</head>
<body class="login-page">
    <div class="login-container">
        <div class="login-box">
            <!-- Header -->
            <div class="login-header">
                <h1 class="title is-3 has-text-white mb-4">
                    <span class="icon is-large">
                        <i class="fas fa-palette"></i>
                    </span>
                </h1>
                <h2 class="subtitle is-5 has-text-white mb-0">
                    <?php echo SITE_NAME; ?>
                </h2>
                <p class="has-text-white-ter">Admin Panel</p>
            </div>
            
            <!-- Body -->
            <div class="login-body">
                <!-- Error Message -->
                <?php if ($error): ?>
                <div class="notification is-danger is-light">
                    <button class="delete"></button>
                    <span class="icon"><i class="fas fa-exclamation-triangle"></i></span>
                    <?php echo htmlspecialchars($error); ?>
                </div>
                <?php endif; ?>
                
                <!-- Success Message -->
                <?php if ($success): ?>
                <div class="notification is-success is-light">
                    <button class="delete"></button>
                    <span class="icon"><i class="fas fa-check"></i></span>
                    <?php echo htmlspecialchars($success); ?>
                </div>
                <?php endif; ?>
                
                <!-- Flash Messages -->
                <?php if (isset($_SESSION['flash_message'])): ?>
                <div class="notification is-<?php echo htmlspecialchars($_SESSION['flash_type'] ?? 'info'); ?> is-light">
                    <button class="delete"></button>
                    <?php echo htmlspecialchars($_SESSION['flash_message']); ?>
                </div>
                <?php 
                unset($_SESSION['flash_message'], $_SESSION['flash_type']); 
                endif; 
                ?>
                
                <!-- Login Form -->
                <form method="post" class="validate-form">
                    <input type="hidden" name="csrf_token" value="<?php echo htmlspecialchars($csrf_token); ?>">
                    
                    <div class="input-group">
                        <span class="input-icon">
                            <i class="fas fa-user"></i>
                        </span>
                        <input type="text" 
                               name="username" 
                               class="form-control" 
                               placeholder="Username or Email"
                               value="<?php echo htmlspecialchars($_POST['username'] ?? ''); ?>"
                               required
                               autocomplete="username">
                    </div>
                    
                    <div class="input-group">
                        <span class="input-icon">
                            <i class="fas fa-lock"></i>
                        </span>
                        <input type="password" 
                               name="password" 
                               class="form-control" 
                               placeholder="Password"
                               required
                               autocomplete="current-password">
                    </div>
                    
                    <div class="field">
                        <label class="checkbox">
                            <input type="checkbox" name="remember_me">
                            Remember me
                        </label>
                    </div>
                    
                    <button type="submit" name="login" class="btn-login">
                        <span class="icon"><i class="fas fa-sign-in-alt"></i></span>
                        <span>Login to Dashboard</span>
                    </button>
                </form>
            </div>
            
            <!-- Footer -->
            <div class="login-footer">
                <p class="is-size-7 has-text-grey">
                    <span class="icon"><i class="fas fa-shield-alt"></i></span>
                    Secure Admin Access
                </p>
                <p class="is-size-7 has-text-grey mt-2">
                    <a href="../index.php" class="has-text-grey">
                        <span class="icon"><i class="fas fa-arrow-left"></i></span>
                        Back to Website
                    </a>
                </p>
            </div>
        </div>
        
        <!-- Installation Notice -->
        <?php if (!file_exists('../config/installed.lock')): ?>
        <div class="notification is-warning mt-4">
            <h4 class="title is-6">System Not Installed</h4>
            <p>Please run the installation first.</p>
            <a href="../install/install.php" class="button is-warning is-small mt-2">
                <span class="icon"><i class="fas fa-download"></i></span>
                <span>Install System</span>
            </a>
        </div>
        <?php endif; ?>
    </div>
    
    <!-- JavaScript -->
    <script src="../assets/js/app.js"></script>
    <script>
        // Auto-hide notifications
        document.querySelectorAll('.notification .delete').forEach(btn => {
            btn.addEventListener('click', () => {
                btn.parentElement.style.display = 'none';
            });
        });
        
        // Auto-hide notifications after 5 seconds
        setTimeout(() => {
            document.querySelectorAll('.notification').forEach(notification => {
                if (!notification.classList.contains('is-danger')) {
                    notification.style.opacity = '0';
                    setTimeout(() => {
                        notification.style.display = 'none';
                    }, 300);
                }
            });
        }, 5000);
        
        // Focus first input
        document.querySelector('input[name="username"]').focus();
    </script>
</body>
</html>
