<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    
    <title><?php echo isset($page_title) ? htmlspecialchars($page_title) . ' - Admin - ' . SITE_NAME : 'Admin - ' . SITE_NAME; ?></title>
    <meta name="description" content="<?php echo isset($page_description) ? htmlspecialchars($page_description) : 'Admin panel for Artist Portfolio Management System'; ?>">
    <meta name="robots" content="noindex, nofollow">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="<?php echo SITE_URL; ?>/assets/images/favicon.ico">
    
    <!-- CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="../assets/css/stormi-palette.css">
    <link rel="stylesheet" href="../assets/css/style.css">
    
    <!-- Additional CSS -->
    <?php if (isset($additional_css)): ?>
        <?php foreach ($additional_css as $css): ?>
            <link rel="stylesheet" href="<?php echo htmlspecialchars($css); ?>">
        <?php endforeach; ?>
    <?php endif; ?>
    
    <style>
        .admin-content {
            padding: 1rem;
            min-height: calc(100vh - 3rem);
        }
        .navbar-admin {
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            min-height: 3rem;
        }
        .navbar-admin .navbar-item {
            padding: 0.5rem 0.75rem;
        }
        .stats-card {
            transition: transform 0.2s ease;
        }
        .stats-card:hover {
            transform: translateY(-2px);
        }
        .media.is-small {
            margin-bottom: 0.75rem;
        }
        .media.is-small:last-child {
            margin-bottom: 0;
        }
        .box {
            box-shadow: 0 0.125rem 0.25rem rgba(0,0,0,0.075);
        }
        .table.is-narrow td, .table.is-narrow th {
            padding: 0.5rem 0.75rem;
        }
        /* Enhanced title and subtitle spacing */
        .title.is-1 { margin-bottom: 1.5rem; }
        .title.is-2 { margin-bottom: 1.25rem; }
        .title.is-3 { margin-bottom: 1.25rem; }
        .title.is-4 { margin-bottom: 1rem; }
        .title.is-5 { margin-bottom: 1rem; }
        .title.is-6 { margin-bottom: 0.75rem; }

        .subtitle.is-4 { margin-bottom: 1.5rem; }
        .subtitle.is-5 { margin-bottom: 1.25rem; }
        .subtitle.is-6 { margin-bottom: 1rem; }
        .subtitle.is-7 { margin-bottom: 0.75rem; }

        /* Title + Subtitle combinations for better visual hierarchy */
        .title + .subtitle { margin-top: 1.25rem !important; }
        .title.is-3 + .subtitle { margin-top: 1.5rem !important; }
        .title.is-4 + .subtitle { margin-top: 1.25rem !important; }
        .title.is-5 + .subtitle { margin-top: 1rem !important; }
        .level.is-mobile { margin-bottom: 0; }
        .box { margin-bottom: 1rem; }
        .card { margin-bottom: 1rem; }
        .button.is-small { font-size: 0.75rem; }
        .navbar-admin .navbar-brand .navbar-item {
            font-weight: 600;
            color: white !important;
        }
        .navbar-admin .navbar-item {
            color: white !important;
        }
        .navbar-admin .navbar-link {
            color: white !important;
        }
        .navbar-admin .navbar-link:hover,
        .navbar-admin .navbar-item:hover {
            background-color: rgba(255, 255, 255, 0.1) !important;
            color: white !important;
        }

        /* Fix dropdown parent hover - when dropdown is open */
        .navbar-admin .navbar-item.has-dropdown.is-active .navbar-link,
        .navbar-admin .navbar-item.has-dropdown:hover .navbar-link {
            background-color: var(--stormi-medium) !important;
            color: white !important;
        }
        .navbar-admin .navbar-dropdown .navbar-item {
            color: #333 !important;
        }

        /* Enhanced Stats Cards */
        .stats-card {
            transition: all 0.3s ease;
            border: none;
            box-shadow: 0 2px 8px rgba(59, 112, 151, 0.1);
        }
        .stats-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 25px rgba(59, 112, 151, 0.15);
        }

        /* Enhanced Dropdown Styling */
        .navbar-admin .navbar-item.has-dropdown .navbar-link::after {
            border-color: white !important;
        }

        .navbar-admin .navbar-dropdown .navbar-item:hover {
            background-color: var(--stormi-light) !important;
            color: white !important;
        }

        .navbar-admin .navbar-dropdown .navbar-item .icon {
            color: inherit !important;
        }

        /* Additional fix for dropdown parent when hovering over dropdown items */
        .navbar-admin .navbar-item.has-dropdown:hover .navbar-link,
        .navbar-admin .navbar-item.has-dropdown.is-hoverable:hover .navbar-link {
            background-color: var(--stormi-medium) !important;
            color: white !important;
        }

        /* Ensure dropdown items have proper contrast */
        .navbar-admin .navbar-dropdown {
            background-color: white !important;
            border: 1px solid rgba(59, 112, 151, 0.1) !important;
            box-shadow: 0 8px 16px rgba(59, 112, 151, 0.15) !important;
        }

        /* Responsive adjustments */
        @media screen and (max-width: 768px) {
            .admin-content {
                padding: 0.5rem;
            }
            .level {
                display: block !important;
            }
            .level-left, .level-right {
                margin-bottom: 1rem;
            }
        }
    </style>
</head>
<body class="<?php echo isset($body_class) ? htmlspecialchars($body_class) : 'admin-page'; ?>">
    
    <!-- Admin Navigation -->
    <nav class="navbar stormi-gradient-primary navbar-admin" role="navigation" aria-label="admin navigation">
        <div class="navbar-brand">
            <a class="navbar-item" href="index.php">
                <span class="icon"><i class="fas fa-palette"></i></span>
                <strong><?php echo SITE_NAME; ?> Admin</strong>
            </a>
            
            <a role="button" class="navbar-burger" aria-label="menu" aria-expanded="false" data-target="adminNavbar">
                <span aria-hidden="true"></span>
                <span aria-hidden="true"></span>
                <span aria-hidden="true"></span>
            </a>
        </div>
        
        <div id="adminNavbar" class="navbar-menu">
            <div class="navbar-start">
                <a class="navbar-item" href="index.php">
                    <span class="icon"><i class="fas fa-tachometer-alt"></i></span>
                    <span>Dashboard</span>
                </a>
                
                <div class="navbar-item has-dropdown is-hoverable">
                    <a class="navbar-link">
                        <span class="icon"><i class="fas fa-users"></i></span>
                        <span>Artists</span>
                    </a>
                    <div class="navbar-dropdown">
                        <a class="navbar-item" href="index.php">
                            <span class="icon"><i class="fas fa-list"></i></span>
                            <span>Manage Artists</span>
                        </a>
                        <a class="navbar-item" href="artists.php?action=create">
                            <span class="icon"><i class="fas fa-plus"></i></span>
                            <span>Add New Artist</span>
                        </a>
                    </div>
                </div>
                
                <a class="navbar-item" href="analytics.php">
                    <span class="icon"><i class="fas fa-chart-bar"></i></span>
                    <span>Analytics</span>
                </a>
                
                <a class="navbar-item" href="qr_codes.php">
                    <span class="icon"><i class="fas fa-qrcode"></i></span>
                    <span>QR Codes</span>
                </a>
            </div>
            
            <div class="navbar-end">
                <div class="navbar-item has-dropdown is-hoverable">
                    <a class="navbar-link">
                        <span class="icon"><i class="fas fa-user"></i></span>
                        <span><?php echo htmlspecialchars($current_user['username'] ?? 'Admin'); ?></span>
                    </a>
                    
                    <div class="navbar-dropdown is-right">
                        <a class="navbar-item" href="profile.php">
                            <span class="icon"><i class="fas fa-user-cog"></i></span>
                            <span>Profile</span>
                        </a>
                        <a class="navbar-item" href="settings.php">
                            <span class="icon"><i class="fas fa-cog"></i></span>
                            <span>Settings</span>
                        </a>
                        <hr class="navbar-divider">
                        <a class="navbar-item" href="logout.php">
                            <span class="icon"><i class="fas fa-sign-out-alt"></i></span>
                            <span>Logout</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </nav>
    
    <!-- Flash Messages -->
    <?php if (isset($_SESSION['flash_message'])): ?>
    <div class="container is-fluid mt-3">
        <div class="notification is-<?php echo htmlspecialchars($_SESSION['flash_type'] ?? 'info'); ?> is-light">
            <button class="delete"></button>
            <?php echo htmlspecialchars($_SESSION['flash_message']); ?>
        </div>
    </div>
    <?php 
    unset($_SESSION['flash_message'], $_SESSION['flash_type']); 
    endif; 
    ?>
    
    <!-- Main Content -->
    <main class="admin-content"><?php // Content will be inserted here ?>
