<?php
require_once 'config/config.php';
require_once 'config/database.php';

echo "<h2>🔧 Fix Analytics Database Schema</h2>";

try {
    $db = Database::getInstance();
    
    echo "<h3>1. Checking Current Table Structure</h3>";
    
    // Check analytics_visits table structure
    $stmt = $db->query("DESCRIBE analytics_visits");
    $columns = $stmt->fetchAll();
    
    echo "<h4>Current analytics_visits columns:</h4>";
    echo "<ul>";
    $has_visitor_hash = false;
    foreach ($columns as $column) {
        echo "<li>{$column['Field']} - {$column['Type']}</li>";
        if ($column['Field'] === 'visitor_hash') {
            $has_visitor_hash = true;
        }
    }
    echo "</ul>";
    
    if (!$has_visitor_hash) {
        echo "<p style='color: red;'>❌ Missing visitor_hash column!</p>";
        
        echo "<h3>2. Adding Missing Columns</h3>";
        
        // Add missing columns
        $alterQueries = [
            "ALTER TABLE analytics_visits ADD COLUMN visitor_hash VARCHAR(64) NOT NULL AFTER visitor_ip",
            "ALTER TABLE analytics_visits ADD INDEX idx_visitor_hash (visitor_hash)"
        ];
        
        foreach ($alterQueries as $query) {
            try {
                $db->query($query);
                echo "<p style='color: green;'>✅ Executed: " . htmlspecialchars($query) . "</p>";
            } catch (Exception $e) {
                echo "<p style='color: orange;'>⚠️ Query may have already been applied: " . htmlspecialchars($e->getMessage()) . "</p>";
            }
        }
        
        echo "<h3>3. Updating Existing Records</h3>";
        
        // Update existing records with visitor hashes
        $stmt = $db->query("SELECT id, visitor_ip, user_agent, visited_at FROM analytics_visits WHERE visitor_hash = '' OR visitor_hash IS NULL");
        $records = $stmt->fetchAll();
        
        $updated = 0;
        foreach ($records as $record) {
            $hash = hash('sha256', $record['visitor_ip'] . $record['user_agent'] . date('Y-m-d', strtotime($record['visited_at'])));
            $db->query("UPDATE analytics_visits SET visitor_hash = ? WHERE id = ?", [$hash, $record['id']]);
            $updated++;
        }
        
        echo "<p style='color: green;'>✅ Updated $updated records with visitor hashes</p>";
        
    } else {
        echo "<p style='color: green;'>✅ visitor_hash column exists</p>";
    }
    
    echo "<h3>4. Verifying Schema</h3>";
    
    // Check again
    $stmt = $db->query("DESCRIBE analytics_visits");
    $columns = $stmt->fetchAll();
    
    $required_columns = ['id', 'artist_id', 'page_type', 'visitor_ip', 'visitor_hash', 'user_agent', 'referrer', 'country', 'city', 'device_type', 'browser', 'visited_at', 'session_id'];
    
    echo "<h4>Required columns check:</h4>";
    foreach ($required_columns as $req_col) {
        $found = false;
        foreach ($columns as $column) {
            if ($column['Field'] === $req_col) {
                $found = true;
                break;
            }
        }
        
        if ($found) {
            echo "<p style='color: green;'>✅ $req_col - Found</p>";
        } else {
            echo "<p style='color: red;'>❌ $req_col - Missing</p>";
        }
    }
    
    echo "<h3>5. Test Analytics After Fix</h3>";
    
    if (isset($_GET['test'])) {
        echo "<p>🧪 Testing analytics after schema fix...</p>";
        
        try {
            require_once 'classes/Analytics.php';
            $analytics = new Analytics();
            
            // Get a test artist
            $stmt = $db->query("SELECT id FROM artists LIMIT 1");
            $artist = $stmt->fetch();
            
            if ($artist) {
                $result = $analytics->trackVisit($artist['id'], 'schema_test');
                if ($result) {
                    echo "<p style='color: green;'>✅ Analytics tracking test successful after schema fix!</p>";
                    
                    // Check if data was inserted
                    $stmt = $db->query("SELECT COUNT(*) as count FROM analytics_visits WHERE page_type = 'schema_test'");
                    $count = $stmt->fetch()['count'];
                    echo "<p style='color: green;'>✅ Found $count test records in database</p>";
                } else {
                    echo "<p style='color: red;'>❌ Analytics tracking test failed</p>";
                }
            } else {
                echo "<p style='color: orange;'>⚠️ No artists found for testing</p>";
            }
            
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ Analytics tracking error: " . $e->getMessage() . "</p>";
        }
        
    } else {
        echo "<p><a href='?test=1' style='background: #3273dc; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🧪 Test Analytics After Fix</a></p>";
    }
    
    echo "<h3>6. Current Data Summary</h3>";
    
    try {
        $stmt = $db->query("SELECT COUNT(*) as total FROM analytics_visits");
        $total = $stmt->fetch()['total'];
        
        $stmt = $db->query("SELECT COUNT(DISTINCT visitor_hash) as unique FROM analytics_visits WHERE visitor_hash IS NOT NULL AND visitor_hash != ''");
        $unique = $stmt->fetch()['unique'];
        
        $stmt = $db->query("SELECT COUNT(*) as artist_visits FROM analytics_visits WHERE artist_id IS NOT NULL");
        $artist_visits = $stmt->fetch()['artist_visits'];
        
        echo "<ul>";
        echo "<li><strong>Total Visits:</strong> $total</li>";
        echo "<li><strong>Unique Visitors:</strong> $unique</li>";
        echo "<li><strong>Artist Page Visits:</strong> $artist_visits</li>";
        echo "</ul>";
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Error getting summary: " . $e->getMessage() . "</p>";
    }
    
    echo "<h3>7. Navigation</h3>";
    echo "<p><a href='admin/analytics.php'>📊 Analytics Dashboard</a></p>";
    echo "<p><a href='debug_analytics.php'>🔍 Debug Analytics</a></p>";
    echo "<p><a href='admin/index.php'>🏠 Admin Dashboard</a></p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Database error: " . htmlspecialchars($e->getMessage()) . "</p>";
}
?>
