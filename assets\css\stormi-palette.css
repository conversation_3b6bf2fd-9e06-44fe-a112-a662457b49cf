/**
 * Stormi Color Palette - Universal Color System
 * Primary colors from the Stormi palette for consistent branding
 */

:root {
    /* Stormi Color Palette */
    --stormi-dark: #3B7097;      /* Deep blue - Primary actions, headers */
    --stormi-medium: #4A8DB7;    /* Medium blue - Secondary elements */
    --stormi-light: #75BDE0;     /* Light blue - Accents, highlights */
    --stormi-lightest: #A2E2F8;  /* Lightest blue - Backgrounds, subtle elements */
    
    /* Semantic Color Assignments */
    --primary-color: var(--stormi-dark);
    --primary-hover: var(--stormi-medium);
    --secondary-color: var(--stormi-medium);
    --accent-color: var(--stormi-light);
    --background-light: var(--stormi-lightest);
    
    /* Gradient Combinations */
    --gradient-primary: linear-gradient(135deg, var(--stormi-dark) 0%, var(--stormi-medium) 100%);
    --gradient-secondary: linear-gradient(135deg, var(--stormi-medium) 0%, var(--stormi-light) 100%);
    --gradient-light: linear-gradient(135deg, var(--stormi-light) 0%, var(--stormi-lightest) 100%);
    --gradient-full: linear-gradient(135deg, var(--stormi-dark) 0%, var(--stormi-medium) 25%, var(--stormi-light) 75%, var(--stormi-lightest) 100%);
    
    /* Text Colors */
    --text-primary: #2c3e50;
    --text-secondary: #5a6c7d;
    --text-light: #7f8c8d;
    --text-white: #ffffff;
    
    /* Status Colors (complementary to Stormi palette) */
    --success-color: #27ae60;
    --warning-color: #f39c12;
    --danger-color: #e74c3c;
    --info-color: var(--stormi-light);
    
    /* Background Colors */
    --bg-primary: #ffffff;
    --bg-secondary: #f8f9fa;
    --bg-light: var(--stormi-lightest);
    --bg-gradient: var(--gradient-full);
    
    /* Border Colors */
    --border-light: rgba(59, 112, 151, 0.1);
    --border-medium: rgba(59, 112, 151, 0.2);
    --border-dark: rgba(59, 112, 151, 0.3);
    
    /* Shadow Colors */
    --shadow-light: rgba(59, 112, 151, 0.1);
    --shadow-medium: rgba(59, 112, 151, 0.15);
    --shadow-dark: rgba(59, 112, 151, 0.25);
    
    /* Button Styles */
    --btn-primary-bg: var(--stormi-dark);
    --btn-primary-hover: var(--stormi-medium);
    --btn-secondary-bg: var(--stormi-light);
    --btn-secondary-hover: var(--stormi-medium);
    
    /* Form Elements */
    --input-border: var(--border-medium);
    --input-focus: var(--stormi-medium);
    --input-bg: var(--bg-primary);
    
    /* Card Elements */
    --card-bg: var(--bg-primary);
    --card-shadow: 0 8px 25px var(--shadow-light);
    --card-hover-shadow: 0 15px 35px var(--shadow-medium);
    
    /* Navigation */
    --nav-bg: var(--gradient-primary);
    --nav-text: var(--text-white);
    --nav-hover: rgba(255, 255, 255, 0.1);
    
    /* Sidebar */
    --sidebar-bg: var(--bg-primary);
    --sidebar-border: var(--border-light);
    --sidebar-hover: var(--background-light);
    
    /* Table Elements */
    --table-header-bg: var(--gradient-secondary);
    --table-row-hover: var(--background-light);
    --table-border: var(--border-light);
    
    /* Modal Elements */
    --modal-bg: var(--bg-primary);
    --modal-overlay: rgba(59, 112, 151, 0.8);
    --modal-shadow: 0 20px 60px var(--shadow-dark);
    
    /* Gallery Elements */
    --gallery-overlay: linear-gradient(135deg, rgba(59, 112, 151, 0.9), rgba(74, 141, 183, 0.9));
    --gallery-shadow: var(--card-shadow);
    --gallery-hover-shadow: var(--card-hover-shadow);
}

/* Utility Classes for Stormi Palette */
.stormi-bg-dark { background-color: var(--stormi-dark) !important; }
.stormi-bg-medium { background-color: var(--stormi-medium) !important; }
.stormi-bg-light { background-color: var(--stormi-light) !important; }
.stormi-bg-lightest { background-color: var(--stormi-lightest) !important; }

.stormi-text-dark { color: var(--stormi-dark) !important; }
.stormi-text-medium { color: var(--stormi-medium) !important; }
.stormi-text-light { color: var(--stormi-light) !important; }
.stormi-text-lightest { color: var(--stormi-lightest) !important; }

.stormi-border-dark { border-color: var(--stormi-dark) !important; }
.stormi-border-medium { border-color: var(--stormi-medium) !important; }
.stormi-border-light { border-color: var(--stormi-light) !important; }

.stormi-gradient-primary { background: var(--gradient-primary) !important; }
.stormi-gradient-secondary { background: var(--gradient-secondary) !important; }
.stormi-gradient-light { background: var(--gradient-light) !important; }
.stormi-gradient-full { background: var(--gradient-full) !important; }

/* Background utility classes */
.has-background-stormi-dark { background-color: var(--stormi-dark) !important; }
.has-background-stormi-medium { background-color: var(--stormi-medium) !important; }
.has-background-stormi-light { background-color: var(--stormi-light) !important; }
.has-background-stormi-lightest { background-color: var(--stormi-lightest) !important; }

/* Text color utility classes */
.has-text-stormi-dark { color: var(--stormi-dark) !important; }
.has-text-stormi-medium { color: var(--stormi-medium) !important; }
.has-text-stormi-light { color: var(--stormi-light) !important; }
.has-text-stormi-lightest { color: var(--stormi-lightest) !important; }

/* Button Overrides for Bulma */
.button.is-stormi-primary {
    background-color: var(--stormi-dark);
    border-color: var(--stormi-dark);
    color: var(--text-white);
}

.button.is-stormi-primary:hover {
    background-color: var(--stormi-medium);
    border-color: var(--stormi-medium);
}

.button.is-stormi-secondary {
    background-color: var(--stormi-light);
    border-color: var(--stormi-light);
    color: var(--text-white);
}

.button.is-stormi-secondary:hover {
    background-color: var(--stormi-medium);
    border-color: var(--stormi-medium);
}

/* Tag Overrides */
.tag.is-stormi-primary {
    background-color: var(--stormi-dark);
    color: var(--text-white);
}

.tag.is-stormi-secondary {
    background-color: var(--stormi-light);
    color: var(--text-white);
}
