<?php
/**
 * Analytics Dashboard
 * Phase 1 & 2 Analytics Implementation
 */

require_once '../config/config.php';
require_once '../config/database.php';
require_once 'includes/auth_check.php';
require_once '../classes/Analytics.php';

$db = Database::getInstance();
$analytics = new Analytics();

// Get date range from query params
$date_from = $_GET['from'] ?? date('Y-m-d', strtotime('-30 days'));
$date_to = $_GET['to'] ?? date('Y-m-d');

// Page configuration
$page_title = 'Analytics Dashboard';
$page_description = 'Comprehensive analytics and insights for artist portfolio visits';

include 'includes/admin_header.php';
?>

<div class="container is-fluid">
    <?php if (isset($_GET['success'])): ?>
    <div class="notification is-success is-light">
        <button class="delete"></button>
        <?php echo htmlspecialchars($_GET['success']); ?>
    </div>
    <?php endif; ?>

    <?php if (isset($error_message)): ?>
    <div class="notification is-danger is-light">
        <button class="delete"></button>
        <?php echo htmlspecialchars($error_message); ?>
    </div>
    <?php endif; ?>
    
    <!-- Analytics Header -->
    <div class="level mb-6">
        <div class="level-left">
            <div class="level-item">
                <div>
                    <h1 class="title is-3 mb-4 stormi-text-dark">Analytics Dashboard</h1>
                    <p class="subtitle is-5 mb-0 has-text-grey-dark">Comprehensive insights and visitor analytics</p>
                </div>
            </div>
        </div>
        <div class="level-right">
            <div class="level-item">
                <div class="field has-addons">
                    <div class="control">
                        <input class="input is-small" type="date" id="date-from" value="<?php echo $date_from; ?>">
                    </div>
                    <div class="control">
                        <input class="input is-small" type="date" id="date-to" value="<?php echo $date_to; ?>">
                    </div>
                    <div class="control">
                        <button class="button is-small is-stormi-primary" onclick="updateDateRange()">
                            <span class="icon"><i class="fas fa-search"></i></span>
                            <span>Filter</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <?php
    // Initialize variables with default values
    $total_views = 0;
    $unique_visitors = 0;
    $popular_artists = [];
    $traffic_sources = [];
    $device_stats = [];
    $country_stats = [];
    $daily_trends = [];
    $browser_stats = [];
    $error_message = null;

    // Get analytics data
    try {
        // Phase 1 Metrics: Essential Analytics

        // 1. Total page views
        $stmt = $db->query("SELECT COUNT(*) as total_views FROM analytics_visits WHERE visited_at BETWEEN ? AND ?",
                          [$date_from . ' 00:00:00', $date_to . ' 23:59:59']);
        $result = $stmt->fetch();
        $total_views = $result['total_views'] ?? 0;

        // 2. Unique visitors
        $stmt = $db->query("SELECT COUNT(DISTINCT COALESCE(visitor_hash, CONCAT(visitor_ip, DATE(visited_at)))) as unique_visitors FROM analytics_visits WHERE visited_at BETWEEN ? AND ?",
                          [$date_from . ' 00:00:00', $date_to . ' 23:59:59']);
        $result = $stmt->fetch();
        $unique_visitors = $result['unique_visitors'] ?? 0;

        // 3. Most popular artists
        $stmt = $db->query("SELECT a.name, a.slug, COUNT(av.id) as views
                           FROM analytics_visits av
                           JOIN artists a ON av.artist_id = a.id
                           WHERE av.visited_at BETWEEN ? AND ? AND av.artist_id IS NOT NULL
                           GROUP BY av.artist_id
                           ORDER BY views DESC LIMIT 10",
                          [$date_from . ' 00:00:00', $date_to . ' 23:59:59']);
        $popular_artists = $stmt->fetchAll() ?: [];

        // 4. Traffic sources
        $stmt = $db->query("SELECT source_type, COUNT(*) as visits
                           FROM analytics_visits av
                           LEFT JOIN analytics_traffic_sources ats ON ats.source_url = av.referer
                           WHERE av.visited_at BETWEEN ? AND ?
                           GROUP BY COALESCE(ats.source_type, 'direct')
                           ORDER BY visits DESC",
                          [$date_from . ' 00:00:00', $date_to . ' 23:59:59']);
        $traffic_sources = $stmt->fetchAll() ?: [];

        // 5. Device types
        $stmt = $db->query("SELECT device_type, COUNT(*) as visits
                           FROM analytics_visits
                           WHERE visited_at BETWEEN ? AND ?
                           GROUP BY device_type
                           ORDER BY visits DESC",
                          [$date_from . ' 00:00:00', $date_to . ' 23:59:59']);
        $device_stats = $stmt->fetchAll() ?: [];

        // 6. Geographic data
        $stmt = $db->query("SELECT country, COUNT(*) as visits
                           FROM analytics_visits
                           WHERE visited_at BETWEEN ? AND ? AND country IS NOT NULL
                           GROUP BY country
                           ORDER BY visits DESC LIMIT 10",
                          [$date_from . ' 00:00:00', $date_to . ' 23:59:59']);
        $country_stats = $stmt->fetchAll() ?: [];

        // Phase 2 Metrics: Advanced Analytics

        // 7. Daily trend data
        $stmt = $db->query("SELECT DATE(visited_at) as date, COUNT(*) as visits, COUNT(DISTINCT COALESCE(visitor_hash, CONCAT(visitor_ip, DATE(visited_at)))) as unique_visitors
                           FROM analytics_visits
                           WHERE visited_at BETWEEN ? AND ?
                           GROUP BY DATE(visited_at)
                           ORDER BY date ASC",
                          [$date_from . ' 00:00:00', $date_to . ' 23:59:59']);
        $daily_trends = $stmt->fetchAll() ?: [];

        // 8. Browser statistics
        $stmt = $db->query("SELECT browser, COUNT(*) as visits
                           FROM analytics_visits
                           WHERE visited_at BETWEEN ? AND ?
                           GROUP BY browser
                           ORDER BY visits DESC",
                          [$date_from . ' 00:00:00', $date_to . ' 23:59:59']);
        $browser_stats = $stmt->fetchAll() ?: [];

        // Phase 3 Metrics: Demographics (with consent)

        // 9. Age range distribution
        $stmt = $db->query("SELECT age_range, COUNT(*) as count
                           FROM analytics_visits
                           WHERE visited_at BETWEEN ? AND ?
                           AND age_range IS NOT NULL
                           AND consent_given = 1
                           GROUP BY age_range
                           ORDER BY count DESC",
                          [$date_from . ' 00:00:00', $date_to . ' 23:59:59']);
        $age_stats = $stmt->fetchAll() ?: [];

        // 10. Gender distribution
        $stmt = $db->query("SELECT gender, COUNT(*) as count
                           FROM analytics_visits
                           WHERE visited_at BETWEEN ? AND ?
                           AND gender IS NOT NULL
                           AND consent_given = 1
                           GROUP BY gender
                           ORDER BY count DESC",
                          [$date_from . ' 00:00:00', $date_to . ' 23:59:59']);
        $gender_stats = $stmt->fetchAll() ?: [];

        // 11. Enhanced location data
        $stmt = $db->query("SELECT country, city, COUNT(*) as visits
                           FROM analytics_visits
                           WHERE visited_at BETWEEN ? AND ?
                           AND country IS NOT NULL
                           AND country != 'Unknown'
                           GROUP BY country, city
                           ORDER BY visits DESC LIMIT 15",
                          [$date_from . ' 00:00:00', $date_to . ' 23:59:59']);
        $enhanced_location_stats = $stmt->fetchAll() ?: [];

        // Phase 4 Metrics: Advanced Analytics

        // 12. User Journey Analysis
        $stmt = $db->query("SELECT
                               COUNT(*) as total_sessions,
                               AVG(time_spent) as avg_time_spent,
                               AVG(scroll_depth) as avg_scroll_depth,
                               AVG(interactions_count) as avg_interactions
                           FROM analytics_user_journeys
                           WHERE entry_time BETWEEN ? AND ?",
                          [$date_from . ' 00:00:00', $date_to . ' 23:59:59']);
        $journey_stats = $stmt->fetch() ?: [];

        // 13. Gallery Interaction Heatmap
        $stmt = $db->query("SELECT interaction_type, COUNT(*) as count
                           FROM analytics_gallery_interactions
                           WHERE timestamp BETWEEN ? AND ?
                           GROUP BY interaction_type
                           ORDER BY count DESC",
                          [$date_from . ' 00:00:00', $date_to . ' 23:59:59']);
        $gallery_interaction_stats = $stmt->fetchAll() ?: [];

        // 14. Social Media Click Analysis
        $stmt = $db->query("SELECT social_platform, COUNT(*) as clicks
                           FROM analytics_social_clicks
                           WHERE timestamp BETWEEN ? AND ?
                           GROUP BY social_platform
                           ORDER BY clicks DESC",
                          [$date_from . ' 00:00:00', $date_to . ' 23:59:59']);
        $social_click_stats = $stmt->fetchAll() ?: [];

        // 15. Conversion Funnel Analysis
        $stmt = $db->query("SELECT funnel_step, COUNT(*) as count
                           FROM analytics_conversion_funnels
                           WHERE timestamp BETWEEN ? AND ?
                           GROUP BY funnel_step
                           ORDER BY FIELD(funnel_step, 'landing', 'profile_view', 'gallery_view', 'social_click', 'contact_attempt')",
                          [$date_from . ' 00:00:00', $date_to . ' 23:59:59']);
        $conversion_funnel_stats = $stmt->fetchAll() ?: [];

    } catch (Exception $e) {
        $error_message = "Error loading analytics data: " . $e->getMessage();
    }
    ?>

    <!-- Phase 1: Essential Metrics -->
    <div class="columns mb-6">
        <div class="column is-3">
            <div class="box has-background-stormi-primary">
                <div class="level">
                    <div class="level-left">
                        <div class="columns is-mobile is-gapless">
                            <div class="column is-narrow">
                                <p class="title is-4 mb-0 has-text-white mr-3"><?php echo number_format($total_views); ?></p>
                            </div>
                            <div class="column">
                                <p class="subtitle is-6 mb-0 has-text-white">Total Views</p>
                            </div>
                        </div>
                    </div>
                    <div class="level-right">
                        <span class="icon has-text-white">
                            <i class="fas fa-eye fa-2x"></i>
                        </span>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="column is-3">
            <div class="box has-background-stormi-secondary">
                <div class="level">
                    <div class="level-left">
                        <div class="columns is-mobile is-gapless">
                            <div class="column is-narrow">
                                <p class="title is-4 mb-0 has-text-white mr-3"><?php echo number_format($unique_visitors); ?></p>
                            </div>
                            <div class="column">
                                <p class="subtitle is-6 mb-0 has-text-white">Unique Visitors</p>
                            </div>
                        </div>
                    </div>
                    <div class="level-right">
                        <span class="icon has-text-white">
                            <i class="fas fa-users fa-2x"></i>
                        </span>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="column is-3">
            <div class="box has-background-stormi-accent">
                <div class="level">
                    <div class="level-left">
                        <div class="columns is-mobile is-gapless">
                            <div class="column is-narrow">
                                <p class="title is-4 mb-0 has-text-white mr-3"><?php echo count($popular_artists); ?></p>
                            </div>
                            <div class="column">
                                <p class="subtitle is-6 mb-0 has-text-white">Artists Viewed</p>
                            </div>
                        </div>
                    </div>
                    <div class="level-right">
                        <span class="icon has-text-white">
                            <i class="fas fa-palette fa-2x"></i>
                        </span>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="column is-3">
            <div class="box has-background-stormi-dark">
                <div class="level">
                    <div class="level-left">
                        <div class="columns is-mobile is-gapless">
                            <div class="column is-narrow">
                                <p class="title is-4 mb-0 has-text-white mr-3"><?php echo count($daily_trends); ?></p>
                            </div>
                            <div class="column">
                                <p class="subtitle is-6 mb-0 has-text-white">Active Days</p>
                            </div>
                        </div>
                    </div>
                    <div class="level-right">
                        <span class="icon has-text-white">
                            <i class="fas fa-calendar fa-2x"></i>
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Analytics Content -->
    <div class="columns">
        <!-- Left Column: Charts and Trends -->
        <div class="column is-8">
            <!-- Daily Trends Chart -->
            <div class="box mb-5">
                <h3 class="title is-5 stormi-text-dark mb-4">
                    <span class="icon"><i class="fas fa-chart-line"></i></span>
                    Daily Visitor Trends
                </h3>
                <canvas id="dailyTrendsChart" width="400" height="200"></canvas>
            </div>
            
            <!-- Most Popular Artists -->
            <div class="box">
                <h3 class="title is-5 stormi-text-dark mb-4">
                    <span class="icon"><i class="fas fa-star"></i></span>
                    Most Popular Artists
                </h3>
                
                <?php if ($popular_artists): ?>
                <div class="table-container">
                    <table class="table is-fullwidth is-hoverable">
                        <thead>
                            <tr>
                                <th>Rank</th>
                                <th>Artist</th>
                                <th>Views</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($popular_artists as $index => $artist): ?>
                            <tr>
                                <td>
                                    <span class="tag is-stormi-primary">#<?php echo $index + 1; ?></span>
                                </td>
                                <td>
                                    <strong><?php echo htmlspecialchars($artist['name']); ?></strong>
                                </td>
                                <td>
                                    <span class="tag is-light"><?php echo number_format($artist['views']); ?> views</span>
                                </td>
                                <td>
                                    <a href="<?php echo SITE_URL; ?>/?page=artist&slug=<?php echo htmlspecialchars($artist['slug']); ?>" 
                                       class="button is-small is-info" target="_blank" title="View Profile">
                                        <span class="icon is-small"><i class="fas fa-eye"></i></span>
                                    </a>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                <?php else: ?>
                <div class="has-text-centered py-4">
                    <span class="icon is-large has-text-grey-light">
                        <i class="fas fa-chart-bar fa-2x"></i>
                    </span>
                    <p class="title is-6 has-text-grey mt-2">No artist views yet</p>
                    <p class="subtitle is-7 has-text-grey">Data will appear as visitors view artist pages</p>
                </div>
                <?php endif; ?>
            </div>
        </div>
        
        <!-- Right Column: Stats and Breakdowns -->
        <div class="column is-4">
            <!-- Traffic Sources -->
            <div class="box mb-5">
                <h3 class="title is-6 stormi-text-dark mb-4">
                    <span class="icon"><i class="fas fa-share-alt"></i></span>
                    Traffic Sources
                </h3>
                
                <?php if ($traffic_sources): ?>
                <?php foreach ($traffic_sources as $source): ?>
                <div class="level is-mobile mb-2">
                    <div class="level-left">
                        <div class="level-item">
                            <span class="tag is-light"><?php echo ucfirst($source['source_type'] ?? 'direct'); ?></span>
                        </div>
                    </div>
                    <div class="level-right">
                        <div class="level-item">
                            <strong><?php echo number_format($source['visits']); ?></strong>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
                <?php else: ?>
                <p class="has-text-grey">No traffic data available</p>
                <?php endif; ?>
            </div>
            
            <!-- Device Types -->
            <div class="box mb-5">
                <h3 class="title is-6 stormi-text-dark mb-4">
                    <span class="icon"><i class="fas fa-mobile-alt"></i></span>
                    Device Types
                </h3>
                
                <?php if ($device_stats): ?>
                <?php foreach ($device_stats as $device): ?>
                <div class="level is-mobile mb-2">
                    <div class="level-left">
                        <div class="level-item">
                            <span class="tag is-light"><?php echo $device['device_type']; ?></span>
                        </div>
                    </div>
                    <div class="level-right">
                        <div class="level-item">
                            <strong><?php echo number_format($device['visits']); ?></strong>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
                <?php else: ?>
                <p class="has-text-grey">No device data available</p>
                <?php endif; ?>
            </div>
            
            <!-- Geographic Data -->
            <div class="box mb-5">
                <h3 class="title is-6 stormi-text-dark mb-4">
                    <span class="icon"><i class="fas fa-globe"></i></span>
                    Top Locations
                </h3>

                <?php if ($enhanced_location_stats): ?>
                <?php foreach (array_slice($enhanced_location_stats, 0, 8) as $location): ?>
                <div class="level is-mobile mb-2">
                    <div class="level-left">
                        <div class="level-item">
                            <span class="tag is-light">
                                <?php echo htmlspecialchars($location['city'] ? $location['city'] . ', ' . $location['country'] : $location['country']); ?>
                            </span>
                        </div>
                    </div>
                    <div class="level-right">
                        <div class="level-item">
                            <strong><?php echo number_format($location['visits']); ?></strong>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
                <?php else: ?>
                <p class="has-text-grey">No geographic data available</p>
                <?php endif; ?>
            </div>

            <!-- Demographics -->
            <?php if (!empty($age_stats) || !empty($gender_stats)): ?>
            <div class="box">
                <h3 class="title is-6 stormi-text-dark mb-4">
                    <span class="icon"><i class="fas fa-users"></i></span>
                    Demographics
                    <span class="tag is-info is-light is-small ml-1">Consented</span>
                </h3>

                <?php if (!empty($age_stats)): ?>
                <div class="mb-4">
                    <h4 class="subtitle is-7 mb-2">Age Ranges</h4>
                    <?php foreach (array_slice($age_stats, 0, 5) as $age): ?>
                    <div class="level is-mobile mb-1">
                        <div class="level-left">
                            <div class="level-item">
                                <span class="tag is-light is-small"><?php echo htmlspecialchars($age['age_range']); ?></span>
                            </div>
                        </div>
                        <div class="level-right">
                            <div class="level-item">
                                <strong><?php echo number_format($age['count']); ?></strong>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
                <?php endif; ?>

                <?php if (!empty($gender_stats)): ?>
                <div>
                    <h4 class="subtitle is-7 mb-2">Gender</h4>
                    <?php foreach ($gender_stats as $gender): ?>
                    <div class="level is-mobile mb-1">
                        <div class="level-left">
                            <div class="level-item">
                                <span class="tag is-light is-small"><?php echo htmlspecialchars(ucfirst($gender['gender'])); ?></span>
                            </div>
                        </div>
                        <div class="level-right">
                            <div class="level-item">
                                <strong><?php echo number_format($gender['count']); ?></strong>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
                <?php endif; ?>
            </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Advanced Analytics Section -->
    <!-- Always show this section to demonstrate the features -->
    <div class="columns mt-6">
        <div class="column is-8">
            <h2 class="title is-4 stormi-text-dark mb-5">
                <span class="icon"><i class="fas fa-chart-line"></i></span>
                Advanced Analytics
                <span class="tag is-primary is-light ml-2">Real-time Insights</span>
            </h2>
        </div>
        <div class="column is-4 has-text-right">
            <a href="heatmap.php" class="button is-info">
                <span class="icon"><i class="fas fa-fire"></i></span>
                <span>View Heatmap</span>
            </a>
            <a href="debug_analytics.php" class="button is-light ml-2">
                <span class="icon"><i class="fas fa-bug"></i></span>
                <span>Debug Data</span>
            </a>
        </div>
    </div>

    <div class="columns">
        <!-- User Journey Analytics -->
        <div class="column is-6">
            <div class="box">
                <h3 class="title is-5 stormi-text-dark mb-4">
                    <span class="icon"><i class="fas fa-route"></i></span>
                    User Journey Insights
                </h3>

                <?php if (!empty($journey_stats) && $journey_stats['total_sessions'] > 0): ?>
                <div class="columns is-multiline">
                    <div class="column is-6">
                        <div class="has-text-centered">
                            <p class="title is-4 stormi-text-dark"><?php echo number_format($journey_stats['total_sessions']); ?></p>
                            <p class="subtitle is-6">Total Sessions</p>
                        </div>
                    </div>
                    <div class="column is-6">
                        <div class="has-text-centered">
                            <p class="title is-4 stormi-text-dark"><?php echo number_format($journey_stats['avg_time_spent'] ?? 0); ?>s</p>
                            <p class="subtitle is-6">Avg. Time Spent</p>
                        </div>
                    </div>
                    <div class="column is-6">
                        <div class="has-text-centered">
                            <p class="title is-4 stormi-text-dark"><?php echo number_format($journey_stats['avg_scroll_depth'] ?? 0); ?>%</p>
                            <p class="subtitle is-6">Avg. Scroll Depth</p>
                        </div>
                    </div>
                    <div class="column is-6">
                        <div class="has-text-centered">
                            <p class="title is-4 stormi-text-dark"><?php echo number_format($journey_stats['avg_interactions'] ?? 0, 1); ?></p>
                            <p class="subtitle is-6">Avg. Interactions</p>
                        </div>
                    </div>
                </div>
                <?php else: ?>
                <p class="has-text-grey">No journey data available yet</p>
                <?php endif; ?>
            </div>
        </div>

        <!-- Gallery Interactions -->
        <div class="column is-6">
            <div class="box">
                <h3 class="title is-5 stormi-text-dark mb-4">
                    <span class="icon"><i class="fas fa-images"></i></span>
                    Gallery Interactions
                </h3>

                <?php if (!empty($gallery_interaction_stats)): ?>
                <?php foreach ($gallery_interaction_stats as $interaction): ?>
                <div class="level is-mobile mb-2">
                    <div class="level-left">
                        <div class="level-item">
                            <span class="tag is-light">
                                <?php echo ucfirst(str_replace('_', ' ', $interaction['interaction_type'])); ?>
                            </span>
                        </div>
                    </div>
                    <div class="level-right">
                        <div class="level-item">
                            <strong><?php echo number_format($interaction['count']); ?></strong>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
                <?php else: ?>
                <p class="has-text-grey">No gallery interaction data available</p>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <div class="columns">
        <!-- Social Media Clicks -->
        <div class="column is-6">
            <div class="box">
                <h3 class="title is-5 stormi-text-dark mb-4">
                    <span class="icon"><i class="fas fa-share-alt"></i></span>
                    Social Media Engagement
                </h3>

                <?php if (!empty($social_click_stats)): ?>
                <?php foreach ($social_click_stats as $social): ?>
                <div class="level is-mobile mb-2">
                    <div class="level-left">
                        <div class="level-item">
                            <span class="tag is-light">
                                <span class="icon is-small">
                                    <i class="fab fa-<?php echo $social['social_platform']; ?>"></i>
                                </span>
                                <span><?php echo ucfirst($social['social_platform']); ?></span>
                            </span>
                        </div>
                    </div>
                    <div class="level-right">
                        <div class="level-item">
                            <strong><?php echo number_format($social['clicks']); ?></strong>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
                <?php else: ?>
                <p class="has-text-grey">No social media click data available</p>
                <?php endif; ?>
            </div>
        </div>

        <!-- Conversion Funnel -->
        <div class="column is-6">
            <div class="box">
                <h3 class="title is-5 stormi-text-dark mb-4">
                    <span class="icon"><i class="fas fa-funnel-dollar"></i></span>
                    Conversion Funnel
                </h3>

                <?php if (!empty($conversion_funnel_stats)): ?>
                <?php
                $total_funnel = array_sum(array_column($conversion_funnel_stats, 'count'));
                foreach ($conversion_funnel_stats as $step):
                    $percentage = $total_funnel > 0 ? ($step['count'] / $total_funnel) * 100 : 0;
                ?>
                <div class="mb-3">
                    <div class="level is-mobile mb-1">
                        <div class="level-left">
                            <div class="level-item">
                                <span><?php echo ucfirst(str_replace('_', ' ', $step['funnel_step'])); ?></span>
                            </div>
                        </div>
                        <div class="level-right">
                            <div class="level-item">
                                <strong><?php echo number_format($step['count']); ?></strong>
                                <span class="has-text-grey ml-2">(<?php echo number_format($percentage, 1); ?>%)</span>
                            </div>
                        </div>
                    </div>
                    <progress class="progress is-primary is-small" value="<?php echo $percentage; ?>" max="100"><?php echo number_format($percentage, 1); ?>%</progress>
                </div>
                <?php endforeach; ?>
                <?php else: ?>
                <p class="has-text-grey">No conversion funnel data available</p>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Chart.js for data visualization -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
// Daily trends chart
const dailyData = <?php echo json_encode($daily_trends); ?>;
const ctx = document.getElementById('dailyTrendsChart').getContext('2d');

new Chart(ctx, {
    type: 'line',
    data: {
        labels: dailyData.map(d => d.date),
        datasets: [{
            label: 'Total Views',
            data: dailyData.map(d => d.visits),
            borderColor: '#6366f1',
            backgroundColor: 'rgba(99, 102, 241, 0.1)',
            tension: 0.4
        }, {
            label: 'Unique Visitors',
            data: dailyData.map(d => d.unique_visitors),
            borderColor: '#8b5cf6',
            backgroundColor: 'rgba(139, 92, 246, 0.1)',
            tension: 0.4
        }]
    },
    options: {
        responsive: true,
        plugins: {
            legend: {
                position: 'top',
            }
        },
        scales: {
            y: {
                beginAtZero: true
            }
        }
    }
});

// Date range filter
function updateDateRange() {
    const from = document.getElementById('date-from').value;
    const to = document.getElementById('date-to').value;
    window.location.href = `analytics.php?from=${from}&to=${to}`;
}
</script>

<?php include 'includes/admin_footer.php'; ?>
