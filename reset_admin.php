<?php
/**
 * Admin User Reset Script
 * Use this to create/reset admin user on live server
 * DELETE THIS FILE AFTER USE FOR SECURITY!
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Admin User Reset</h1>";

// Include config
if (file_exists('config/config.php')) {
    require_once 'config/config.php';
    require_once 'config/database.php';
} else {
    echo "❌ Config files not found<br>";
    exit;
}

// Admin credentials - CHANGE THESE!
$admin_username = 'admin';
$admin_password = 'admin123'; // CHANGE THIS!
$admin_email = '<EMAIL>'; // CHANGE THIS!

echo "<p><strong>⚠️ WARNING:</strong> This will create/update admin user with:</p>";
echo "<ul>";
echo "<li>Username: {$admin_username}</li>";
echo "<li>Password: {$admin_password}</li>";
echo "<li>Email: {$admin_email}</li>";
echo "</ul>";

if (isset($_POST['create_admin'])) {
    try {
        $db = Database::getInstance();
        
        // Hash the password
        $hashed_password = password_hash($admin_password, PASSWORD_DEFAULT);
        
        // Check if admin user exists
        $stmt = $db->query("SELECT id FROM users WHERE username = ? OR email = ?", [$admin_username, $admin_email]);
        $existing_user = $stmt->fetch();
        
        if ($existing_user) {
            // Update existing user
            $sql = "UPDATE users SET 
                    username = ?, 
                    email = ?, 
                    password = ?, 
                    role = 'admin',
                    is_active = 1,
                    updated_at = NOW()
                    WHERE id = ?";
            
            $db->query($sql, [$admin_username, $admin_email, $hashed_password, $existing_user['id']]);
            echo "✅ Admin user updated successfully!<br>";
        } else {
            // Create new user
            $sql = "INSERT INTO users (username, email, password, role, is_active, created_at) 
                    VALUES (?, ?, ?, 'admin', 1, NOW())";
            
            $db->query($sql, [$admin_username, $admin_email, $hashed_password]);
            echo "✅ Admin user created successfully!<br>";
        }
        
        echo "<p><strong>✅ Admin user is ready!</strong></p>";
        echo "<p><a href='admin/login.php'>Go to Admin Login</a></p>";
        echo "<p><strong>🔒 IMPORTANT: Delete this file (reset_admin.php) for security!</strong></p>";
        
    } catch (Exception $e) {
        echo "❌ Error: " . $e->getMessage() . "<br>";
    }
} else {
    echo "<form method='POST'>";
    echo "<p><strong>⚠️ Make sure to:</strong></p>";
    echo "<ol>";
    echo "<li>Change the admin credentials in this file first</li>";
    echo "<li>Test database connection with test_connection.php</li>";
    echo "<li>Delete this file after creating admin user</li>";
    echo "</ol>";
    echo "<button type='submit' name='create_admin' style='background: #e74c3c; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;'>Create/Update Admin User</button>";
    echo "</form>";
}

echo "<hr>";
echo "<h2>Current Users in Database:</h2>";

try {
    $db = Database::getInstance();
    $stmt = $db->query("SELECT id, username, email, role, is_active, created_at FROM users ORDER BY created_at DESC");
    $users = $stmt->fetchAll();
    
    if ($users) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>ID</th><th>Username</th><th>Email</th><th>Role</th><th>Active</th><th>Created</th></tr>";
        foreach ($users as $user) {
            echo "<tr>";
            echo "<td>{$user['id']}</td>";
            echo "<td>{$user['username']}</td>";
            echo "<td>{$user['email']}</td>";
            echo "<td>{$user['role']}</td>";
            echo "<td>" . ($user['is_active'] ? 'Yes' : 'No') . "</td>";
            echo "<td>{$user['created_at']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>No users found in database.</p>";
    }
} catch (Exception $e) {
    echo "❌ Error loading users: " . $e->getMessage() . "<br>";
}
?>
