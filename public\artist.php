<?php
/**
 * Artist Profile Page
 * Displays individual artist portfolio with gallery and information
 */

// Get artist slug from URL
$slug = $_GET['slug'] ?? '';

if (empty($slug)) {
    header('Location: admin/login.php');
    exit;
}

// Get artist data from database
$artist = null;
$gallery_images = [];

try {
    $db = Database::getInstance();

    // Re-initialize language to pick up URL parameter
    $language = new Language();

    // Get artist by slug with translation
    $artist = $language->getArtistBySlugWithTranslation($slug);

    if (!$artist) {
        header('Location: admin/login.php');
        exit;
    }

    // Track analytics for this artist page visit
    try {
        require_once 'classes/Analytics.php';
        $analytics = new Analytics();
        $analytics->trackVisit($artist['id'], 'page_view');
    } catch (Exception $e) {
        // Analytics tracking failed, but don't break the page
        error_log("Analytics tracking error: " . $e->getMessage());
    }
    
    // Get gallery images
    $stmt = $db->query("SELECT * FROM artist_galleries WHERE artist_id = ? AND is_active = 1 ORDER BY sort_order ASC, created_at DESC", [$artist['id']]);
    $gallery_images = $stmt->fetchAll();
    
    // Track visit for analytics
    $stmt = $db->query("INSERT INTO analytics_visits (artist_id, visitor_ip, user_agent, referer, visit_type, visited_at) VALUES (?, ?, ?, ?, 'page_view', NOW())", [
        $artist['id'],
        $_SERVER['REMOTE_ADDR'] ?? '',
        $_SERVER['HTTP_USER_AGENT'] ?? '',
        $_SERVER['HTTP_REFERER'] ?? ''
    ]);
    
} catch (Exception $e) {
    error_log("Artist profile error: " . $e->getMessage());
    header('Location: admin/login.php');
    exit;
}

// Page configuration
$page_title = $artist['name'] . ' - Artist Portfolio';
$page_description = $artist['bio'] ? substr(strip_tags($artist['bio']), 0, 160) : 'View the portfolio of ' . $artist['name'];
$page_keywords = $artist['name'] . ', artist, portfolio, ' . ($artist['main_medium'] ?? 'art') . ', ' . ($artist['country'] ?? '');
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">

    <!-- SEO Meta Tags -->
    <title><?php echo htmlspecialchars($page_title); ?></title>
    <meta name="description" content="<?php echo htmlspecialchars($page_description); ?>">
    <meta name="keywords" content="<?php echo htmlspecialchars($page_keywords); ?>">
    <meta name="author" content="<?php echo htmlspecialchars($artist['name']); ?>">
    <meta name="robots" content="index, follow">
    <link rel="canonical" href="<?php echo SITE_URL . '/artist/' . urlencode($artist['slug']); ?>">

    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="<?php echo htmlspecialchars($page_title); ?>">
    <meta property="og:description" content="<?php echo htmlspecialchars($page_description); ?>">
    <meta property="og:type" content="profile">
    <meta property="og:url" content="<?php echo SITE_URL . '/artist/' . urlencode($artist['slug']); ?>">
    <meta property="og:site_name" content="<?php echo SITE_NAME; ?>">
    <?php if ($artist['avatar_image']): ?>
    <meta property="og:image" content="<?php echo SITE_URL . '/' . htmlspecialchars($artist['avatar_image']); ?>">
    <meta property="og:image:alt" content="<?php echo htmlspecialchars($artist['name']); ?> - Artist Profile Photo">
    <?php endif; ?>

    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="<?php echo htmlspecialchars($page_title); ?>">
    <meta name="twitter:description" content="<?php echo htmlspecialchars($page_description); ?>">
    <?php if ($artist['avatar_image']): ?>
    <meta name="twitter:image" content="<?php echo SITE_URL . '/' . htmlspecialchars($artist['avatar_image']); ?>">
    <?php endif; ?>

    <!-- Schema.org Structured Data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "Person",
        "name": "<?php echo addslashes($artist['name']); ?>",
        "jobTitle": "Artist",
        "description": "<?php echo addslashes($page_description); ?>",
        <?php if ($artist['avatar_image']): ?>
        "image": "<?php echo SITE_URL . '/' . addslashes($artist['avatar_image']); ?>",
        <?php endif; ?>
        <?php if ($artist['website_url']): ?>
        "url": "<?php echo addslashes($artist['website_url']); ?>",
        <?php endif; ?>
        "address": {
            "@type": "PostalAddress",
            "addressCountry": "<?php echo addslashes($artist['country'] ?? ''); ?>"
        },
        "sameAs": [
            <?php
            $social_links = [];
            if ($artist['website_url']) $social_links[] = '"' . addslashes($artist['website_url']) . '"';
            if ($artist['instagram_url']) $social_links[] = '"' . addslashes($artist['instagram_url']) . '"';
            if ($artist['facebook_url']) $social_links[] = '"' . addslashes($artist['facebook_url']) . '"';
            if ($artist['twitter_url']) $social_links[] = '"' . addslashes($artist['twitter_url']) . '"';
            if ($artist['linkedin_url']) $social_links[] = '"' . addslashes($artist['linkedin_url']) . '"';
            if ($artist['youtube_url']) $social_links[] = '"' . addslashes($artist['youtube_url']) . '"';
            echo implode(',', $social_links);
            ?>
        ]
    }
    </script>

    <!-- CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="assets/css/stormi-palette.css">

    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: var(--bg-gradient);
        }
        .main-container {
            min-height: 100vh;
            padding: 2rem 0;
        }
    </style>
</head>
<body class="artist-profile">

<div class="main-container"><?php // Content will continue below ?>

<!-- Artist Profile -->
<div class="artist-profile-container">
    <div class="container">
        <div class="columns is-centered">
            <div class="column is-10">

                <!-- Language Selector -->
                <div class="language-selector-container">
                    <div class="language-selector">
                        <?php
                        $current_lang = $language->getCurrentLanguage();
                        $available_languages = $language->getAvailableLanguages();
                        ?>
                        <?php foreach ($available_languages as $lang): ?>
                        <button class="language-btn <?php echo $current_lang === $lang['code'] ? 'active' : ''; ?>"
                                data-lang="<?php echo $lang['code']; ?>"
                                data-artist-slug="<?php echo htmlspecialchars($artist['slug']); ?>">
                            <span class="lang-text"><?php echo htmlspecialchars($lang['native_name']); ?></span>
                        </button>
                        <?php endforeach; ?>
                    </div>
                </div>

                <!-- Artist Header Card -->
                <div class="artist-header-card">
                    <div class="columns is-vcentered is-mobile">
                        <!-- Artist Photo -->
                        <div class="column is-narrow-desktop is-full-mobile">
                            <div class="artist-photo-container">
                                <?php if ($artist['avatar_image']): ?>
                                <img src="<?php echo htmlspecialchars($artist['avatar_image']); ?>"
                                     alt="<?php echo htmlspecialchars($artist['name']); ?>"
                                     class="artist-photo">
                                <?php else: ?>
                                <div class="artist-photo-placeholder">
                                    <?php echo strtoupper(substr($artist['name'], 0, 1)); ?>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>

                        <!-- Artist Info -->
                        <div class="column">
                            <div class="artist-info">
                                <h1 class="artist-name"><?php echo htmlspecialchars($artist['name']); ?></h1>

                                <div class="artist-details">
                                    <?php if ($artist['country']): ?>
                                    <div class="detail-item">
                                        <span class="detail-icon"><i class="fas fa-map-marker-alt"></i></span>
                                        <span class="detail-text"><?php echo htmlspecialchars($artist['country']); ?></span>
                                    </div>
                                    <?php endif; ?>

                                    <?php if ($artist['main_medium']): ?>
                                    <div class="detail-item">
                                        <span class="detail-icon"><i class="fas fa-palette"></i></span>
                                        <span class="detail-text"><?php echo htmlspecialchars($artist['main_medium']); ?></span>
                                    </div>
                                    <?php endif; ?>

                                    <?php if ($artist['main_subjects']): ?>
                                    <div class="detail-item">
                                        <span class="detail-icon"><i class="fas fa-paint-brush"></i></span>
                                        <span class="detail-text"><?php echo htmlspecialchars($artist['main_subjects']); ?></span>
                                    </div>
                                    <?php endif; ?>
                                </div>

                                <?php if ($artist['bio']): ?>
                                <div class="artist-description">
                                    <p><?php echo nl2br(htmlspecialchars($artist['bio'])); ?></p>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Social Media & Website Section -->
                <?php if ($artist['instagram_url'] || $artist['facebook_url'] || $artist['website_url'] || $artist['youtube_url'] || $artist['twitter_url'] || $artist['linkedin_url']): ?>
                <div class="social-section">
                    <h3 class="section-title">
                        <span class="icon"><i class="fas fa-share-alt"></i></span>
                        Connect with <?php echo htmlspecialchars($artist['name']); ?>
                    </h3>
                    <div class="social-links-grid">
                        <?php if ($artist['website_url']): ?>
                        <a href="<?php echo htmlspecialchars($artist['website_url']); ?>" target="_blank" class="social-link website">
                            <div class="social-icon"><i class="fas fa-globe"></i></div>
                            <span class="social-label">Website</span>
                        </a>
                        <?php endif; ?>

                        <?php if ($artist['instagram_url']): ?>
                        <a href="<?php echo htmlspecialchars($artist['instagram_url']); ?>" target="_blank" class="social-link instagram">
                            <div class="social-icon"><i class="fab fa-instagram"></i></div>
                            <span class="social-label">Instagram</span>
                        </a>
                        <?php endif; ?>

                        <?php if ($artist['facebook_url']): ?>
                        <a href="<?php echo htmlspecialchars($artist['facebook_url']); ?>" target="_blank" class="social-link facebook">
                            <div class="social-icon"><i class="fab fa-facebook"></i></div>
                            <span class="social-label">Facebook</span>
                        </a>
                        <?php endif; ?>

                        <?php if ($artist['youtube_url']): ?>
                        <a href="<?php echo htmlspecialchars($artist['youtube_url']); ?>" target="_blank" class="social-link youtube">
                            <div class="social-icon"><i class="fab fa-youtube"></i></div>
                            <span class="social-label">YouTube</span>
                        </a>
                        <?php endif; ?>

                        <?php if ($artist['twitter_url']): ?>
                        <a href="<?php echo htmlspecialchars($artist['twitter_url']); ?>" target="_blank" class="social-link twitter">
                            <div class="social-icon"><i class="fab fa-twitter"></i></div>
                            <span class="social-label">Twitter</span>
                        </a>
                        <?php endif; ?>

                        <?php if ($artist['linkedin_url']): ?>
                        <a href="<?php echo htmlspecialchars($artist['linkedin_url']); ?>" target="_blank" class="social-link linkedin">
                            <div class="social-icon"><i class="fab fa-linkedin"></i></div>
                            <span class="social-label">LinkedIn</span>
                        </a>
                        <?php endif; ?>
                    </div>
                </div>
                <?php endif; ?>

                <!-- Masonry Gallery Section -->
                <?php if ($gallery_images): ?>
                <div class="gallery-section">
                    <h3 class="section-title">
                        <span class="icon"><i class="fas fa-images"></i></span>
                        Gallery
                        <span class="gallery-count">(<?php echo count($gallery_images); ?> works)</span>
                    </h3>

                    <div class="masonry-gallery" id="masonryGallery">
                        <?php foreach ($gallery_images as $index => $image): ?>
                        <div class="masonry-item" onclick="openModal(<?php echo $index; ?>)">
                            <div class="masonry-image-container">
                                <img src="<?php echo htmlspecialchars($image['image_path']); ?>"
                                     alt="<?php echo htmlspecialchars($image['title'] ?? $artist['name'] . ' artwork'); ?>"
                                     class="masonry-image"
                                     loading="lazy">
                                <div class="masonry-overlay">
                                    <div class="masonry-overlay-content">
                                        <div class="overlay-icon">
                                            <i class="fas fa-search-plus"></i>
                                        </div>
                                        <?php if ($image['title']): ?>
                                        <div class="overlay-title"><?php echo htmlspecialchars($image['title']); ?></div>
                                        <?php endif; ?>
                                        <?php if ($image['description']): ?>
                                        <div class="overlay-description"><?php echo htmlspecialchars($image['description']); ?></div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                </div>
                <?php endif; ?>
                
            </div>
        </div>
    </div>
</div>

<!-- Image Modal -->
<div id="imageModal" class="modal">
    <div class="modal-background" onclick="closeModal()"></div>

    <!-- Navigation Arrows - Positioned at screen edges -->
    <button id="prevButton" class="gallery-nav-button gallery-nav-prev" onclick="previousImage()">
        <i class="fas fa-chevron-left"></i>
    </button>
    <button id="nextButton" class="gallery-nav-button gallery-nav-next" onclick="nextImage()">
        <i class="fas fa-chevron-right"></i>
    </button>

    <div class="modal-content">
        <div class="gallery-modal-container">
            <!-- Image Container -->
            <div class="gallery-modal-image-container">
                <img id="modalImage" src="" alt="" class="gallery-modal-image">
            </div>

            <!-- Image Info -->
            <div id="modalInfo" class="gallery-modal-info">
                <div class="gallery-modal-header">
                    <h3 id="modalTitle" class="gallery-modal-title"></h3>
                    <div id="modalCounter" class="gallery-modal-counter"></div>
                </div>
                <p id="modalDescription" class="gallery-modal-description"></p>
            </div>
        </div>
    </div>
    <button class="modal-close is-large" onclick="closeModal()"></button>
</div>

<style>
/* Artist Profile Styles */
.artist-profile-container {
    min-height: 100vh;
    background: var(--bg-gradient);
    padding: 0.6rem 0;
}

/* Mobile-first approach */
@media (max-width: 768px) {
    .artist-profile-container {
        padding: 0.2rem 0;
    }
}

.artist-header-card {
    background: var(--card-bg);
    border-radius: 20px;
    padding: 2.5rem;
    box-shadow: var(--card-shadow);
    margin-bottom: 2rem;
    backdrop-filter: blur(10px);
}

.artist-photo-container {
    text-align: center;
    margin-right: 2rem;
}

.artist-photo {
    width: 180px;
    height: 180px;
    border-radius: 50%;
    object-fit: cover;
    border: 6px solid var(--bg-primary);
    box-shadow: 0 10px 30px var(--shadow-medium);
}

.artist-photo-placeholder {
    width: 180px;
    height: 180px;
    border-radius: 50%;
    background: var(--gradient-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-white);
    font-size: 3.5rem;
    font-weight: bold;
    border: 6px solid var(--bg-primary);
    box-shadow: 0 10px 30px var(--shadow-medium);
    margin: 0 auto;
}

.artist-info {
    padding-left: 1rem;
}

.artist-name {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 1rem;
    line-height: 1.2;
}

.artist-details {
    margin-bottom: 1.5rem;
}

.detail-item {
    display: flex;
    align-items: center;
    margin-bottom: 0.75rem;
    font-size: 1.1rem;
}

.detail-icon {
    width: 24px;
    margin-right: 12px;
    color: var(--stormi-medium);
    font-size: 1rem;
}

.detail-text {
    color: var(--text-primary);
    font-weight: 500;
}

.artist-description {
    color: var(--text-secondary);
    font-size: 1rem;
    line-height: 1.6;
    max-width: 600px;
}

.artist-description p {
    margin-bottom: 0;
}

/* Language Selector Styles */
.language-selector-container {
    margin-bottom: 0.6rem;
    display: flex;
    justify-content: center;
    padding: 0.3rem 0;
}

.language-selector {
    display: flex;
    gap: 0.5rem;
    background: rgba(255, 255, 255, 0.95);
    padding: 0.5rem;
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(59, 112, 151, 0.15);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    position: relative;
}

.language-selector::before {
    content: '';
    position: absolute;
    top: -1px;
    left: -1px;
    right: -1px;
    bottom: -1px;
    background: linear-gradient(135deg, var(--stormi-light), var(--stormi-medium), var(--stormi-dark));
    border-radius: 16px;
    z-index: -1;
    opacity: 0.1;
}

.language-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 12px;
    background: transparent;
    color: var(--stormi-dark);
    font-weight: 500;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    min-width: 100px;
}



.language-btn.active {
    background: linear-gradient(135deg, var(--stormi-dark), var(--stormi-medium));
    color: white;
    box-shadow: 0 4px 16px rgba(59, 112, 151, 0.3);
    transform: translateY(-2px);
}

.language-btn.active::before {
    opacity: 0;
}





.lang-text {
    font-weight: 600;
    letter-spacing: 0.025em;
}





/* Responsive adjustments for language selector */
@media screen and (max-width: 768px) {
    .language-selector {
        gap: 0.25rem;
        padding: 0.5rem;
        border-radius: 12px;
    }

    .language-btn {
        min-width: 80px;
        padding: 0.6rem 1rem;
        font-size: 0.85rem;
    }

    .language-selector-container {
        margin-bottom: 0.45rem;
        padding: 0.15rem 0;
    }
}

@media screen and (max-width: 480px) {
    .language-selector {
        gap: 0.2rem;
        padding: 0.4rem;
    }

    .language-btn {
        min-width: 70px;
        padding: 0.5rem 0.8rem;
        font-size: 0.8rem;
    }
}

/* Social Media Section */
.social-section {
    background: var(--card-bg);
    border-radius: 20px;
    padding: 2rem;
    box-shadow: var(--card-shadow);
    margin-bottom: 2rem;
}

.section-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
}

.section-title .icon {
    margin-right: 0.75rem;
    color: var(--stormi-medium);
}

.social-links-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
    gap: 1rem;
}

.social-link {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 1.25rem;
    border-radius: 15px;
    text-decoration: none;
    transition: all 0.3s ease;
    background: #f8f9fa;
    border: 2px solid transparent;
}

.social-link:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.15);
    text-decoration: none;
}

.social-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 0.75rem;
    font-size: 1.5rem;
    color: white;
}

.social-label {
    font-size: 0.9rem;
    font-weight: 600;
    color: var(--text-primary);
}

.social-link.website .social-icon { background: var(--gradient-primary); }
.social-link.instagram .social-icon { background: linear-gradient(45deg, #f09433 0%,#e6683c 25%,#dc2743 50%,#cc2366 75%,#bc1888 100%); }
.social-link.facebook .social-icon { background: #1877f2; }
.social-link.youtube .social-icon { background: #ff0000; }
.social-link.twitter .social-icon { background: #1da1f2; }
.social-link.linkedin .social-icon { background: #0077b5; }

/* Gallery Section */
.gallery-section {
    background: var(--card-bg);
    border-radius: 20px;
    padding: 2rem;
    box-shadow: var(--card-shadow);
}

.gallery-count {
    font-size: 0.9rem;
    font-weight: 400;
    color: var(--stormi-medium);
    margin-left: 0.5rem;
}

/* Masonry Gallery Layout */
.masonry-gallery {
    column-count: 3;
    column-gap: 1.5rem;
    column-fill: balance;
}

.masonry-item {
    display: inline-block;
    width: 100%;
    margin-bottom: 1.5rem;
    break-inside: avoid;
    cursor: pointer;
    transition: all 0.3s ease;
    -webkit-tap-highlight-color: transparent;
    touch-action: manipulation;
}

.masonry-item:hover {
    transform: translateY(-5px);
}

.masonry-image-container {
    position: relative;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: var(--gallery-shadow);
    transition: all 0.3s ease;
}

.masonry-item:hover .masonry-image-container {
    box-shadow: var(--gallery-hover-shadow);
}

.masonry-image {
    width: 100%;
    height: auto;
    display: block;
    transition: transform 0.3s ease;
}

.masonry-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--gallery-overlay);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: all 0.3s ease;
    padding: 1.5rem;
}

.masonry-item:hover .masonry-overlay {
    opacity: 1;
}

.masonry-item:hover .masonry-image {
    transform: scale(1.05);
}

.masonry-overlay-content {
    text-align: center;
    color: white;
    max-width: 100%;
}

.overlay-icon {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    opacity: 0.9;
}

.overlay-title {
    font-size: 1.1rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    line-height: 1.3;
}

.overlay-description {
    font-size: 0.9rem;
    opacity: 0.9;
    line-height: 1.4;
}

/* Modal Styles */
.modal {
    z-index: 9999;
}

.modal-content {
    max-width: 95vw;
    max-height: 95vh;
    position: relative;
}

.gallery-modal-container {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    height: 100%;
}

.gallery-modal-image-container {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    max-height: 80vh;
    margin-bottom: 1rem;
}

.gallery-modal-image {
    max-width: 100%;
    max-height: 80vh;
    object-fit: contain;
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    transition: opacity 0.3s ease;
}

/* Navigation Buttons */
.gallery-nav-button {
    position: fixed;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(0, 0, 0, 0.7);
    color: white;
    border: none;
    border-radius: 50%;
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 1.4rem;
    transition: all 0.3s ease;
    z-index: 10000;
    backdrop-filter: blur(10px);
    border: 2px solid rgba(255, 255, 255, 0.1);
    opacity: 0;
    animation: fadeInNav 0.5s ease 0.3s forwards;
}

.gallery-nav-button:hover {
    background: rgba(0, 0, 0, 0.9);
    transform: translateY(-50%) scale(1.1);
    border-color: rgba(255, 255, 255, 0.3);
}

.gallery-nav-button:disabled {
    opacity: 0.3;
    cursor: not-allowed;
}

.gallery-nav-button:disabled:hover {
    transform: translateY(-50%);
    background: rgba(0, 0, 0, 0.7);
    border-color: rgba(255, 255, 255, 0.1);
}

.gallery-nav-prev {
    left: 30px;
}

.gallery-nav-next {
    right: 30px;
}

/* Navigation button animations */
@keyframes fadeInNav {
    from {
        opacity: 0;
        transform: translateY(-50%) scale(0.8);
    }
    to {
        opacity: 1;
        transform: translateY(-50%) scale(1);
    }
}

/* Modal Info */
.gallery-modal-info {
    text-align: center;
    color: white;
    max-width: 600px;
    padding: 0 1rem;
}

.gallery-modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
    flex-wrap: wrap;
    gap: 1rem;
}

.gallery-modal-title {
    font-size: 1.5rem;
    font-weight: 600;
    margin: 0;
    color: white;
}

.gallery-modal-counter {
    background: rgba(255, 255, 255, 0.2);
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 500;
}

.gallery-modal-description {
    font-size: 1rem;
    line-height: 1.5;
    margin: 0;
    opacity: 0.9;
}

/* Responsive Design */
@media (max-width: 768px) {
    .artist-profile-container {
        padding: 0.2rem 0;
    }

    .artist-header-card {
        padding: 1rem;
        margin: 0 0.5rem 1rem 0.5rem;
        border-radius: 15px;
    }

    /* Compact header layout for mobile */
    .artist-header-card .columns {
        flex-direction: column;
        align-items: center;
        text-align: center;
    }

    .artist-photo, .artist-photo-placeholder {
        width: 100px;
        height: 100px;
        border-width: 3px;
    }

    .artist-photo-placeholder {
        font-size: 2rem;
    }

    .artist-name {
        font-size: 1.8rem;
        margin-bottom: 0.5rem;
    }

    .artist-photo-container {
        margin-right: 0;
        margin-bottom: 1rem;
    }

    .artist-info {
        padding-left: 0;
        text-align: center;
    }

    .artist-bio {
        font-size: 0.9rem;
        line-height: 1.4;
        margin-bottom: 1rem;
    }

    .artist-details {
        flex-direction: column;
        gap: 0.5rem;
    }

    .artist-detail {
        font-size: 0.85rem;
        justify-content: center;
    }

    /* Compact sections */
    .social-section, .gallery-section {
        margin: 0 0.5rem 1rem 0.5rem;
        padding: 1rem;
        border-radius: 15px;
    }

    .section-title {
        font-size: 1.3rem;
        margin-bottom: 1rem;
    }

    .social-links-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 0.8rem;
    }

    .social-link {
        padding: 0.8rem;
        border-radius: 12px;
    }

    .social-icon {
        width: 40px;
        height: 40px;
        font-size: 1.2rem;
        margin-bottom: 0.5rem;
    }

    .social-label {
        font-size: 0.8rem;
    }

    /* Compact gallery */
    .masonry-gallery {
        column-count: 2;
        column-gap: 0.8rem;
    }

    .masonry-item {
        margin-bottom: 1rem;
    }
}

@media (max-width: 480px) {
    .social-links-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .masonry-gallery {
        column-count: 1;
        column-gap: 0;
    }

    .masonry-item {
        margin-bottom: 1rem;
        border-radius: 12px;
        overflow: hidden;
    }

    .masonry-overlay {
        padding: 0.8rem;
    }

    .overlay-title {
        font-size: 0.9rem;
        font-weight: 600;
    }

    .overlay-description {
        font-size: 0.75rem;
        line-height: 1.3;
    }

    .gallery-count {
        font-size: 0.8rem;
    }

    /* Modal responsive styles */
    .gallery-nav-button {
        width: 45px;
        height: 45px;
        font-size: 1rem;
    }

    .gallery-nav-prev {
        left: 10px;
    }

    .gallery-nav-next {
        right: 10px;
    }

    .gallery-modal-title {
        font-size: 1.1rem;
    }

    .gallery-modal-description {
        font-size: 0.9rem;
    }

    .gallery-modal-counter {
        font-size: 0.8rem;
        padding: 0.2rem 0.6rem;
    }

    .gallery-modal-header {
        flex-direction: column;
        gap: 0.5rem;
        margin-bottom: 0.8rem;
    }

    .gallery-modal-info {
        padding: 0 0.5rem;
    }
}

/* Extra small mobile devices */
@media (max-width: 480px) {
    .artist-header-card {
        margin: 0 0.3rem 0.8rem 0.3rem;
        padding: 0.8rem;
    }

    .artist-photo, .artist-photo-placeholder {
        width: 80px;
        height: 80px;
    }

    .artist-photo-placeholder {
        font-size: 1.5rem;
    }

    .artist-name {
        font-size: 1.5rem;
    }

    .artist-bio {
        font-size: 0.85rem;
    }

    .social-section, .gallery-section {
        margin: 0 0.3rem 0.8rem 0.3rem;
        padding: 0.8rem;
    }

    .section-title {
        font-size: 1.1rem;
    }

    .social-links-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 0.6rem;
    }

    .social-link {
        padding: 0.6rem;
    }

    .social-icon {
        width: 35px;
        height: 35px;
        font-size: 1rem;
    }

    .masonry-gallery {
        column-count: 1;
        column-gap: 0;
    }

    .masonry-item {
        margin-bottom: 0.8rem;
    }
}
</style>

<script>
// Gallery data
const galleryImages = <?php echo json_encode($gallery_images); ?>;
let currentImageIndex = 0;



function updateModalContent() {
    const image = galleryImages[currentImageIndex];
    const modal = document.getElementById('imageModal');
    const modalImage = document.getElementById('modalImage');
    const modalTitle = document.getElementById('modalTitle');
    const modalDescription = document.getElementById('modalDescription');
    const modalCounter = document.getElementById('modalCounter');
    const prevButton = document.getElementById('prevButton');
    const nextButton = document.getElementById('nextButton');

    // Add loading state
    modalImage.style.opacity = '0.5';

    // Update image with loading handling
    const newImage = new Image();
    newImage.onload = function() {
        modalImage.src = this.src;
        modalImage.style.opacity = '1';
    };
    newImage.src = image.image_path;
    modalImage.alt = image.title || 'Gallery image';

    // Update title and description
    modalTitle.textContent = image.title || 'Untitled';
    modalDescription.textContent = image.description || '';

    // Update counter
    modalCounter.textContent = `${currentImageIndex + 1} of ${galleryImages.length}`;

    // Update navigation buttons
    prevButton.disabled = currentImageIndex === 0;
    nextButton.disabled = currentImageIndex === galleryImages.length - 1;

    // Hide navigation if only one image
    if (galleryImages.length <= 1) {
        prevButton.style.display = 'none';
        nextButton.style.display = 'none';
    } else {
        prevButton.style.display = 'flex';
        nextButton.style.display = 'flex';
    }
}

function previousImage() {
    if (currentImageIndex > 0) {
        currentImageIndex--;
        updateModalContent();
    }
}

function nextImage() {
    if (currentImageIndex < galleryImages.length - 1) {
        currentImageIndex++;
        updateModalContent();
    }
}

function handleKeyPress(event) {
    switch(event.key) {
        case 'ArrowLeft':
            event.preventDefault();
            previousImage();
            break;
        case 'ArrowRight':
            event.preventDefault();
            nextImage();
            break;
        case 'Escape':
            event.preventDefault();
            closeModal();
            break;
    }
}

// Touch/Swipe support for mobile
let touchStartX = 0;
let touchEndX = 0;

function handleTouchStart(event) {
    touchStartX = event.changedTouches[0].screenX;
}

function handleTouchEnd(event) {
    touchEndX = event.changedTouches[0].screenX;
    handleSwipe();
}

function handleSwipe() {
    const swipeThreshold = 50;
    const swipeDistance = touchEndX - touchStartX;

    if (Math.abs(swipeDistance) > swipeThreshold) {
        if (swipeDistance > 0) {
            // Swipe right - previous image
            previousImage();
        } else {
            // Swipe left - next image
            nextImage();
        }
    }
}

// Add touch event listeners when modal opens
function openModal(index) {
    currentImageIndex = index;
    updateModalContent();
    const modal = document.getElementById('imageModal');
    modal.classList.add('is-active');

    // Add event listeners
    document.addEventListener('keydown', handleKeyPress);
    modal.addEventListener('touchstart', handleTouchStart);
    modal.addEventListener('touchend', handleTouchEnd);
}

// Remove touch event listeners when modal closes
function closeModal() {
    const modal = document.getElementById('imageModal');
    modal.classList.remove('is-active');

    // Remove event listeners
    document.removeEventListener('keydown', handleKeyPress);
    modal.removeEventListener('touchstart', handleTouchStart);
    modal.removeEventListener('touchend', handleTouchEnd);
}

// Close modal with escape key
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        closeModal();
    }
});

// Language switching functionality
document.addEventListener('DOMContentLoaded', function() {
    const languageButtons = document.querySelectorAll('.language-btn');

    languageButtons.forEach(button => {
        button.addEventListener('click', function() {
            const selectedLang = this.getAttribute('data-lang');

            // Create URL with language parameter
            const currentUrl = new URL(window.location);
            currentUrl.searchParams.set('lang', selectedLang);

            // Redirect to the same page with new language
            window.location.href = currentUrl.toString();
        });
    });
});
</script>

</div> <!-- Close main-container -->
</body>
</html>
