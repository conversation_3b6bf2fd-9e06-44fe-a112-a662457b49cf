<?php
/**
 * Artist Management System with Multi-language Support
 */

require_once '../config/config.php';
require_once '../config/database.php';
require_once 'includes/auth_check.php';

$db = Database::getInstance();
$action = $_GET['action'] ?? 'list';
$artist_id = $_GET['id'] ?? null;

// Get available languages
$stmt = $db->query("SELECT * FROM languages WHERE is_active = 1 ORDER BY sort_order ASC");
$languages = $stmt->fetchAll();

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if ($action === 'create' || $action === 'edit') {
        try {
            $db->beginTransaction();
            
            // Handle main artist data
            $slug = strtolower(trim(preg_replace('/[^A-Za-z0-9-]+/', '-', $_POST['name_en'])));

            // Handle avatar image upload
            $avatar_image = '';
            if (isset($_FILES['avatar_image']) && $_FILES['avatar_image']['error'] === UPLOAD_ERR_OK) {
                $upload_dir = '../' . UPLOAD_DIR . 'artists/';
                if (!is_dir($upload_dir)) {
                    mkdir($upload_dir, 0755, true);
                }

                $file_extension = strtolower(pathinfo($_FILES['avatar_image']['name'], PATHINFO_EXTENSION));

                if (in_array($file_extension, ALLOWED_EXTENSIONS)) {
                    $filename = $slug . '_avatar_' . time() . '.' . $file_extension;
                    $upload_path = $upload_dir . $filename;

                    if (move_uploaded_file($_FILES['avatar_image']['tmp_name'], $upload_path)) {
                        $avatar_image = UPLOAD_DIR . 'artists/' . $filename;
                    }
                }
            }

            // For edit mode, keep existing avatar if no new one uploaded
            if ($action === 'edit' && empty($avatar_image)) {
                $stmt = $db->query("SELECT avatar_image FROM artists WHERE id = ?", [$artist_id]);
                $existing = $stmt->fetch();
                $avatar_image = $existing['avatar_image'] ?? '';
            }
            
            if ($action === 'create') {
                // Create new artist
                $sql = "INSERT INTO artists (name, slug, email, phone, website_url, country,
                        avatar_image, facebook_url, instagram_url, youtube_url, twitter_url, linkedin_url,
                        is_active, is_featured, sort_order)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

                $db->query($sql, [
                    $_POST['name_en'], // Use English name as primary
                    $slug,
                    $_POST['email'] ?? '',
                    $_POST['phone'] ?? '',
                    $_POST['website_url'] ?? '',
                    $_POST['country'] ?? '',
                    $avatar_image,
                    $_POST['facebook_url'] ?? '',
                    $_POST['instagram_url'] ?? '',
                    $_POST['youtube_url'] ?? '',
                    $_POST['twitter_url'] ?? '',
                    $_POST['linkedin_url'] ?? '',
                    isset($_POST['is_active']) ? 1 : 0,
                    isset($_POST['is_featured']) ? 1 : 0,
                    $_POST['sort_order'] ?? 0
                ]);
                
                $artist_id = $db->lastInsertId();
            } else {
                // Update existing artist
                $sql = "UPDATE artists SET name = ?, email = ?, phone = ?, website_url = ?,
                        country = ?, avatar_image = ?, facebook_url = ?, instagram_url = ?,
                        youtube_url = ?, twitter_url = ?, linkedin_url = ?,
                        is_active = ?, is_featured = ?, sort_order = ?
                        WHERE id = ?";

                $db->query($sql, [
                    $_POST['name_en'], // Use English name as primary
                    $_POST['email'] ?? '',
                    $_POST['phone'] ?? '',
                    $_POST['website_url'] ?? '',
                    $_POST['country'] ?? '',
                    $avatar_image,
                    $_POST['facebook_url'] ?? '',
                    $_POST['instagram_url'] ?? '',
                    $_POST['youtube_url'] ?? '',
                    $_POST['twitter_url'] ?? '',
                    $_POST['linkedin_url'] ?? '',
                    isset($_POST['is_active']) ? 1 : 0,
                    isset($_POST['is_featured']) ? 1 : 0,
                    $_POST['sort_order'] ?? 0,
                    $artist_id
                ]);
            }
            
            // Handle translations for each language
            foreach ($languages as $lang) {
                $lang_code = $lang['code'];
                
                // Check if translation exists
                $stmt = $db->query("SELECT id FROM artist_translations WHERE artist_id = ? AND language_code = ?", 
                                 [$artist_id, $lang_code]);
                $translation = $stmt->fetch();
                
                if ($translation) {
                    // Update existing translation
                    $sql = "UPDATE artist_translations SET 
                            name = ?, bio = ?, description = ?, main_medium = ?, main_subjects = ?,
                            education = ?, exhibitions = ?, awards = ?, artist_statement = ?
                            WHERE artist_id = ? AND language_code = ?";
                    
                    $db->query($sql, [
                        $_POST["name_{$lang_code}"] ?? '',
                        $_POST["bio_{$lang_code}"] ?? '',
                        $_POST["description_{$lang_code}"] ?? '',
                        $_POST["main_medium_{$lang_code}"] ?? '',
                        $_POST["main_subjects_{$lang_code}"] ?? '',
                        $_POST["education_{$lang_code}"] ?? '',
                        $_POST["exhibitions_{$lang_code}"] ?? '',
                        $_POST["awards_{$lang_code}"] ?? '',
                        $_POST["artist_statement_{$lang_code}"] ?? '',
                        $artist_id,
                        $lang_code
                    ]);
                } else {
                    // Create new translation
                    $sql = "INSERT INTO artist_translations 
                            (artist_id, language_code, name, bio, description, main_medium, main_subjects,
                             education, exhibitions, awards, artist_statement) 
                            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
                    
                    $db->query($sql, [
                        $artist_id,
                        $lang_code,
                        $_POST["name_{$lang_code}"] ?? '',
                        $_POST["bio_{$lang_code}"] ?? '',
                        $_POST["description_{$lang_code}"] ?? '',
                        $_POST["main_medium_{$lang_code}"] ?? '',
                        $_POST["main_subjects_{$lang_code}"] ?? '',
                        $_POST["education_{$lang_code}"] ?? '',
                        $_POST["exhibitions_{$lang_code}"] ?? '',
                        $_POST["awards_{$lang_code}"] ?? '',
                        $_POST["artist_statement_{$lang_code}"] ?? ''
                    ]);
                }
            }

            // Handle gallery image uploads (only for edit mode)
            if ($action === 'edit' && isset($_FILES['gallery_images']) && !empty($_FILES['gallery_images']['name'][0])) {
                $upload_dir = '../' . UPLOAD_DIR . 'gallery/';
                if (!is_dir($upload_dir)) {
                    mkdir($upload_dir, 0755, true);
                }

                foreach ($_FILES['gallery_images']['name'] as $key => $filename) {
                    if ($_FILES['gallery_images']['error'][$key] === UPLOAD_ERR_OK) {
                        $file_extension = strtolower(pathinfo($filename, PATHINFO_EXTENSION));
                        if (in_array($file_extension, ALLOWED_EXTENSIONS)) {
                            $new_filename = $slug . '_gallery_' . time() . '_' . $key . '.' . $file_extension;
                            $upload_path = $upload_dir . $new_filename;

                            if (move_uploaded_file($_FILES['gallery_images']['tmp_name'][$key], $upload_path)) {
                                // Insert gallery image record
                                $gallery_sql = "INSERT INTO artist_galleries (artist_id, image_path, title, sort_order, is_active)
                                               VALUES (?, ?, ?, ?, 1)";
                                $db->query($gallery_sql, [
                                    $artist_id,
                                    UPLOAD_DIR . 'gallery/' . $new_filename,
                                    pathinfo($filename, PATHINFO_FILENAME), // Use original filename as title
                                    0 // Default sort order
                                ]);
                            }
                        }
                    }
                }
            }

            $db->commit();

            $success_message = $action === 'create' ? 'Artist created successfully!' : 'Artist updated successfully!';
            header("Location: artists.php?success=" . urlencode($success_message));
            exit;

        } catch (Exception $e) {
            $db->rollback();
            $error_message = "Error saving artist: " . $e->getMessage();
            error_log("DEBUG: Exception during artist save: " . $e->getMessage());
        }
    }
}

// Handle gallery image deletion
if ($action === 'delete_gallery' && $artist_id && isset($_GET['gallery_id'])) {
    try {
        $gallery_id = $_GET['gallery_id'];

        // Get gallery image data for file cleanup
        $stmt = $db->query("SELECT image_path FROM artist_galleries WHERE id = ? AND artist_id = ?", [$gallery_id, $artist_id]);
        $gallery_data = $stmt->fetch();

        if ($gallery_data) {
            // Delete the file
            $file_path = '../' . $gallery_data['image_path'];
            if (file_exists($file_path)) {
                unlink($file_path);
            }

            // Delete from database
            $db->query("DELETE FROM artist_galleries WHERE id = ? AND artist_id = ?", [$gallery_id, $artist_id]);

            header("Location: artists.php?action=edit&id=$artist_id&success=" . urlencode('Gallery image deleted successfully!'));
            exit;
        }
    } catch (Exception $e) {
        header("Location: artists.php?action=edit&id=$artist_id&error=" . urlencode('Error deleting gallery image: ' . $e->getMessage()));
        exit;
    }
}

// Handle GET requests (like delete)
if ($action === 'delete' && $artist_id) {
    try {
        error_log("DEBUG: Starting artist deletion for ID: " . $artist_id);
        $db->beginTransaction();

        // Get artist data for cleanup
        $stmt = $db->query("SELECT avatar_image FROM artists WHERE id = ?", [$artist_id]);
        $artist_data = $stmt->fetch();
        error_log("DEBUG: Artist data retrieved: " . json_encode($artist_data));

        // Delete translations (ignore if table doesn't exist)
        try {
            $db->query("DELETE FROM artist_translations WHERE artist_id = ?", [$artist_id]);
            error_log("DEBUG: Deleted translations");
        } catch (Exception $e) {
            error_log("DEBUG: Translation delete failed (table may not exist): " . $e->getMessage());
        }

        // Delete gallery images (ignore if table doesn't exist)
        try {
            $db->query("DELETE FROM artist_galleries WHERE artist_id = ?", [$artist_id]);
            error_log("DEBUG: Deleted gallery images");
        } catch (Exception $e) {
            error_log("DEBUG: Gallery delete failed (table may not exist): " . $e->getMessage());
        }

        // Delete QR mappings (ignore if table doesn't exist)
        try {
            $db->query("DELETE FROM qr_artist_mappings WHERE artist_id = ?", [$artist_id]);
            error_log("DEBUG: Deleted QR mappings");
        } catch (Exception $e) {
            error_log("DEBUG: QR mapping delete failed (table may not exist): " . $e->getMessage());
        }

        // Delete artist (main table)
        $result = $db->query("DELETE FROM artists WHERE id = ?", [$artist_id]);
        error_log("DEBUG: Artist deleted, affected rows: " . $result->rowCount());

        // Clean up avatar image file
        if ($artist_data && $artist_data['avatar_image']) {
            $file_path = '../' . $artist_data['avatar_image'];
            if (file_exists($file_path)) {
                unlink($file_path);
                error_log("DEBUG: Deleted avatar file: " . $file_path);
            }
        }

        $db->commit();
        error_log("DEBUG: Transaction committed successfully");

        // Determine redirect based on referrer
        $redirect_url = "artists.php";
        if (isset($_SERVER['HTTP_REFERER']) && strpos($_SERVER['HTTP_REFERER'], 'index.php') !== false) {
            $redirect_url = "index.php";
        }

        header("Location: " . $redirect_url . "?success=" . urlencode("Artist deleted successfully!"));
        exit;

    } catch (Exception $e) {
        $db->rollback();
        error_log("DEBUG: Delete failed: " . $e->getMessage());
        $error_message = "Error deleting artist: " . $e->getMessage();

        // Show error instead of white page
        echo "<h2>Delete Error</h2>";
        echo "<p>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
        echo "<p><a href='index.php'>Back to Dashboard</a></p>";
        exit;
    }
}

// Get artist data for editing
$artist = null;
$translations = [];
if ($action === 'edit' && $artist_id) {
    $stmt = $db->query("SELECT * FROM artists WHERE id = ?", [$artist_id]);
    $artist = $stmt->fetch();
    
    if ($artist) {
        // Get translations
        $stmt = $db->query("SELECT * FROM artist_translations WHERE artist_id = ?", [$artist_id]);
        $trans_data = $stmt->fetchAll();
        
        foreach ($trans_data as $trans) {
            $translations[$trans['language_code']] = $trans;
        }
    }
}

// Redirect list action to dashboard
if ($action === 'list') {
    header('Location: index.php');
    exit;
}

$page_title = $action === 'create' ? 'Add New Artist' : 
              ($action === 'edit' ? 'Edit Artist' : 'Artists');

include 'includes/admin_header.php';
?>

<div class="container is-fluid">
    <?php if (isset($_GET['success'])): ?>
    <div class="notification is-success is-light">
        <button class="delete"></button>
        <?php echo htmlspecialchars($_GET['success']); ?>
    </div>
    <?php endif; ?>
    
    <?php if (isset($error_message)): ?>
    <div class="notification is-danger is-light">
        <button class="delete"></button>
        <?php echo htmlspecialchars($error_message); ?>
    </div>
    <?php endif; ?>

    <?php if ($action === 'create' || $action === 'edit'): ?>
    <!-- Create/Edit Artist Form -->
    <div class="level mb-6">
        <div class="level-left">
            <div class="level-item">
                <div>
                    <h1 class="title is-3 mb-4 stormi-text-dark">
                        <?php echo $action === 'create' ? 'Add New Artist' : 'Edit Artist'; ?>
                    </h1>
                    <p class="subtitle is-5 mb-0 has-text-grey-dark">
                        <?php echo $action === 'create' ? 'Create a new artist profile with multi-language support' : 'Update artist information and translations'; ?>
                    </p>
                </div>
            </div>
        </div>
        <div class="level-right">
            <div class="level-item">
                <a href="artists.php" class="button is-light">
                    <span class="icon"><i class="fas fa-arrow-left"></i></span>
                    <span>Back to Artists</span>
                </a>
            </div>
        </div>
    </div>

    <form method="POST" enctype="multipart/form-data">
        <!-- Basic Information Row -->
        <div class="columns mb-5">
            <!-- Main Information -->
            <div class="column is-6">
                <div class="box">
                    <h2 class="title is-5 stormi-text-dark mb-4">
                        <span class="icon"><i class="fas fa-user"></i></span>
                        Basic Information
                    </h2>
                    
                    <div class="field">
                        <label class="label">Email</label>
                        <div class="control has-icons-left">
                            <input class="input" type="email" name="email" 
                                   value="<?php echo htmlspecialchars($artist['email'] ?? ''); ?>">
                            <span class="icon is-small is-left">
                                <i class="fas fa-envelope"></i>
                            </span>
                        </div>
                    </div>
                    
                    <div class="field">
                        <label class="label">Phone</label>
                        <div class="control has-icons-left">
                            <input class="input" type="tel" name="phone" 
                                   value="<?php echo htmlspecialchars($artist['phone'] ?? ''); ?>">
                            <span class="icon is-small is-left">
                                <i class="fas fa-phone"></i>
                            </span>
                        </div>
                    </div>
                    
                    <div class="field">
                        <label class="label">Website URL</label>
                        <div class="control has-icons-left">
                            <input class="input" type="url" name="website_url" 
                                   value="<?php echo htmlspecialchars($artist['website_url'] ?? ''); ?>">
                            <span class="icon is-small is-left">
                                <i class="fas fa-globe"></i>
                            </span>
                        </div>
                    </div>
                    
                    <div class="field" style="position: relative;">
                        <label class="label">Country</label>
                        <div class="control has-icons-left">
                            <input class="input" type="text" name="country" id="country-input"
                                   value="<?php echo htmlspecialchars($artist['country'] ?? ''); ?>"
                                   placeholder="Type to search countries..." autocomplete="off">
                            <span class="icon is-small is-left">
                                <i class="fas fa-map-marker-alt"></i>
                            </span>
                        </div>
                        <div id="country-dropdown" style="display: none;"></div>
                    </div>
                    
                    <div class="field">
                        <label class="label">Avatar Image</label>
                        <div class="control">
                            <div class="file has-name">
                                <label class="file-label">
                                    <input class="file-input" type="file" name="avatar_image" accept="image/*">
                                    <span class="file-cta">
                                        <span class="file-icon">
                                            <i class="fas fa-upload"></i>
                                        </span>
                                        <span class="file-label">
                                            Choose image...
                                        </span>
                                    </span>
                                    <span class="file-name">
                                        No file selected
                                    </span>
                                </label>
                            </div>
                        </div>
                        <?php if (isset($artist['avatar_image']) && $artist['avatar_image']): ?>
                        <div class="mt-3">
                            <p class="help">Current image:</p>
                            <figure class="image is-64x64">
                                <img src="<?php echo htmlspecialchars('../' . $artist['avatar_image']); ?>"
                                     alt="Current avatar" class="is-rounded">
                            </figure>
                        </div>
                        <?php endif; ?>
                        <p class="help">Supported formats: JPG, PNG, GIF, WebP. Max size: 5MB</p>
                    </div>

                    <div class="field">
                        <label class="label">Sort Order</label>
                        <div class="control">
                            <input class="input" type="number" name="sort_order" min="0"
                                   value="<?php echo htmlspecialchars($artist['sort_order'] ?? '0'); ?>">
                        </div>
                    </div>
                </div>
            </div>

            <!-- Social Media Links -->
            <div class="column is-6">
                <div class="box">
                    <h2 class="title is-5 stormi-text-dark mb-4">
                        <span class="icon"><i class="fas fa-share-alt"></i></span>
                        Social Media Links
                    </h2>

                    <div class="columns is-multiline">
                        <div class="column is-6">
                            <div class="field">
                                <label class="label">Facebook URL</label>
                                <div class="control has-icons-left">
                                    <input class="input" type="url" name="facebook_url"
                                           value="<?php echo htmlspecialchars($artist['facebook_url'] ?? ''); ?>"
                                           placeholder="https://facebook.com/username">
                                    <span class="icon is-small is-left">
                                        <i class="fab fa-facebook"></i>
                                    </span>
                                </div>
                            </div>
                        </div>

                        <div class="column is-6">
                            <div class="field">
                                <label class="label">Instagram URL</label>
                                <div class="control has-icons-left">
                                    <input class="input" type="url" name="instagram_url"
                                           value="<?php echo htmlspecialchars($artist['instagram_url'] ?? ''); ?>"
                                           placeholder="https://instagram.com/username">
                                    <span class="icon is-small is-left">
                                        <i class="fab fa-instagram"></i>
                                    </span>
                                </div>
                            </div>
                        </div>

                        <div class="column is-6">
                            <div class="field">
                                <label class="label">YouTube URL</label>
                                <div class="control has-icons-left">
                                    <input class="input" type="url" name="youtube_url"
                                           value="<?php echo htmlspecialchars($artist['youtube_url'] ?? ''); ?>"
                                           placeholder="https://youtube.com/channel/...">
                                    <span class="icon is-small is-left">
                                        <i class="fab fa-youtube"></i>
                                    </span>
                                </div>
                            </div>
                        </div>

                        <div class="column is-6">
                            <div class="field">
                                <label class="label">Twitter URL</label>
                                <div class="control has-icons-left">
                                    <input class="input" type="url" name="twitter_url"
                                           value="<?php echo htmlspecialchars($artist['twitter_url'] ?? ''); ?>"
                                           placeholder="https://twitter.com/username">
                                    <span class="icon is-small is-left">
                                        <i class="fab fa-twitter"></i>
                                    </span>
                                </div>
                            </div>
                        </div>

                        <div class="column is-12">
                            <div class="field">
                                <label class="label">LinkedIn URL</label>
                                <div class="control has-icons-left">
                                    <input class="input" type="url" name="linkedin_url"
                                           value="<?php echo htmlspecialchars($artist['linkedin_url'] ?? ''); ?>"
                                           placeholder="https://linkedin.com/in/username">
                                    <span class="icon is-small is-left">
                                        <i class="fab fa-linkedin"></i>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="field">
                        <div class="control">
                            <label class="checkbox">
                                <input type="checkbox" name="is_active" <?php echo ($artist['is_active'] ?? 1) ? 'checked' : ''; ?>>
                                Active
                            </label>
                        </div>
                    </div>

                    <div class="field">
                        <div class="control">
                            <label class="checkbox">
                                <input type="checkbox" name="is_featured" <?php echo ($artist['is_featured'] ?? 0) ? 'checked' : ''; ?>>
                                Local Artist (Sri Lanka)
                            </label>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Gallery Management Row -->
        <div class="columns mb-5">
            <div class="column is-12">
                <div class="box">
                    <h2 class="title is-5 stormi-text-dark mb-4">
                        <span class="icon"><i class="fas fa-images"></i></span>
                        Gallery Management
                    </h2>

                    <?php if ($action === 'edit' && $artist_id): ?>
                    <?php
                    // Get existing gallery images
                    $stmt = $db->query("SELECT * FROM artist_galleries WHERE artist_id = ? ORDER BY sort_order ASC, created_at DESC", [$artist_id]);
                    $gallery_images = $stmt->fetchAll();
                    ?>

                    <div class="field">
                        <label class="label">Add New Gallery Images</label>
                        <div class="control">
                            <div class="file has-name">
                                <label class="file-label">
                                    <input class="file-input" type="file" name="gallery_images[]" accept="image/*" multiple>
                                    <span class="file-cta">
                                        <span class="file-icon">
                                            <i class="fas fa-upload"></i>
                                        </span>
                                        <span class="file-label">
                                            Choose images...
                                        </span>
                                    </span>
                                    <span class="file-name">
                                        No files selected
                                    </span>
                                </label>
                            </div>
                        </div>
                        <p class="help">You can select multiple images. Supported formats: JPG, PNG, GIF, WebP. Max size: 5MB each</p>
                    </div>

                    <?php if ($gallery_images): ?>
                    <div class="field">
                        <label class="label">Current Gallery Images</label>
                        <div class="gallery-grid" style="display: grid; grid-template-columns: repeat(auto-fill, minmax(150px, 1fr)); gap: 1rem;">
                            <?php foreach ($gallery_images as $image): ?>
                            <div class="gallery-item" style="position: relative; border: 1px solid #ddd; border-radius: 8px; overflow: hidden;">
                                <img src="<?php echo htmlspecialchars('../' . $image['image_path']); ?>"
                                     alt="<?php echo htmlspecialchars($image['title'] ?? ''); ?>"
                                     style="width: 100%; height: 120px; object-fit: cover;">
                                <div style="padding: 0.5rem; font-size: 0.8rem;">
                                    <strong><?php echo htmlspecialchars($image['title'] ?: 'Untitled'); ?></strong>
                                    <br>
                                    <small class="has-text-grey">Order: <?php echo $image['sort_order']; ?></small>
                                    <br>
                                    <a href="?action=delete_gallery&id=<?php echo $artist_id; ?>&gallery_id=<?php echo $image['id']; ?>"
                                       class="button is-small is-danger mt-2"
                                       onclick="return confirm('Delete this image?')">
                                        <span class="icon is-small"><i class="fas fa-trash"></i></span>
                                    </a>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                    <?php endif; ?>

                    <?php else: ?>
                    <div class="notification is-info is-light">
                        <p><strong>Note:</strong> Gallery management will be available after creating the artist profile.</p>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Multi-language Content Row -->
        <div class="columns mb-5">
            <div class="column is-12">
                <div class="box">
                    <h2 class="title is-5 stormi-text-dark mb-4">
                        <span class="icon"><i class="fas fa-language"></i></span>
                        Multi-language Content
                    </h2>
                    
                    <!-- Language Tabs -->
                    <div class="tabs is-boxed">
                        <ul>
                            <?php foreach ($languages as $index => $lang): ?>
                            <li class="<?php echo $index === 0 ? 'is-active' : ''; ?>" data-tab="<?php echo $lang['code']; ?>">
                                <a>
                                    <span class="icon is-small">
                                        <i class="fas fa-<?php echo $lang['code'] === 'en' ? 'globe' : 'language'; ?>"></i>
                                    </span>
                                    <span><?php echo htmlspecialchars($lang['native_name']); ?></span>
                                    <?php if ($lang['is_default']): ?>
                                    <span class="tag is-small is-primary ml-2">Default</span>
                                    <?php endif; ?>
                                </a>
                            </li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                    
                    <!-- Language Content Panels -->
                    <?php foreach ($languages as $index => $lang): ?>
                    <div class="tab-content <?php echo $index === 0 ? 'is-active' : ''; ?>" id="tab-<?php echo $lang['code']; ?>">
                        <div class="columns is-multiline">
                            <div class="column is-6">
                                <div class="field">
                                    <label class="label">
                                        Artist Name 
                                        <?php if ($lang['is_default']): ?>
                                        <span class="has-text-danger">*</span>
                                        <?php endif; ?>
                                    </label>
                                    <div class="control">
                                        <input class="input" type="text" name="name_<?php echo $lang['code']; ?>" 
                                               value="<?php echo htmlspecialchars($translations[$lang['code']]['name'] ?? ''); ?>"
                                               <?php echo $lang['is_default'] ? 'required' : ''; ?>>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="column is-6">
                                <div class="field">
                                    <label class="label">Main Medium</label>
                                    <div class="control">
                                        <input class="input" type="text" name="main_medium_<?php echo $lang['code']; ?>" 
                                               value="<?php echo htmlspecialchars($translations[$lang['code']]['main_medium'] ?? ''); ?>"
                                               placeholder="e.g., Oil Painting, Sculpture, Digital Art">
                                    </div>
                                </div>
                            </div>
                            
                            <div class="column is-12">
                                <div class="field">
                                    <label class="label">Main Subjects</label>
                                    <div class="control">
                                        <input class="input" type="text" name="main_subjects_<?php echo $lang['code']; ?>" 
                                               value="<?php echo htmlspecialchars($translations[$lang['code']]['main_subjects'] ?? ''); ?>"
                                               placeholder="e.g., Portraits, Landscapes, Abstract">
                                    </div>
                                </div>
                            </div>
                            
                            <div class="column is-12">
                                <div class="field">
                                    <label class="label">Bio</label>
                                    <div class="control">
                                        <textarea class="textarea" name="bio_<?php echo $lang['code']; ?>" rows="3"
                                                  placeholder="Brief biography of the artist"><?php echo htmlspecialchars($translations[$lang['code']]['bio'] ?? ''); ?></textarea>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="column is-12">
                                <div class="field">
                                    <label class="label">Description</label>
                                    <div class="control">
                                        <textarea class="textarea" name="description_<?php echo $lang['code']; ?>" rows="4"
                                                  placeholder="Detailed description of the artist's work and style"><?php echo htmlspecialchars($translations[$lang['code']]['description'] ?? ''); ?></textarea>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="column is-12">
                                <div class="field">
                                    <label class="label">Education</label>
                                    <div class="control">
                                        <textarea class="textarea" name="education_<?php echo $lang['code']; ?>" rows="3"
                                                  placeholder="Educational background and qualifications"><?php echo htmlspecialchars($translations[$lang['code']]['education'] ?? ''); ?></textarea>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="column is-12">
                                <div class="field">
                                    <label class="label">Exhibitions</label>
                                    <div class="control">
                                        <textarea class="textarea" name="exhibitions_<?php echo $lang['code']; ?>" rows="4"
                                                  placeholder="Notable exhibitions and shows"><?php echo htmlspecialchars($translations[$lang['code']]['exhibitions'] ?? ''); ?></textarea>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="column is-12">
                                <div class="field">
                                    <label class="label">Awards</label>
                                    <div class="control">
                                        <textarea class="textarea" name="awards_<?php echo $lang['code']; ?>" rows="3"
                                                  placeholder="Awards and recognitions"><?php echo htmlspecialchars($translations[$lang['code']]['awards'] ?? ''); ?></textarea>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="column is-12">
                                <div class="field">
                                    <label class="label">Artist Statement</label>
                                    <div class="control">
                                        <textarea class="textarea" name="artist_statement_<?php echo $lang['code']; ?>" rows="4"
                                                  placeholder="Personal artist statement or philosophy"><?php echo htmlspecialchars($translations[$lang['code']]['artist_statement'] ?? ''); ?></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
        
        <!-- Form Actions -->
        <div class="field is-grouped is-grouped-right">
            <div class="control">
                <a href="artists.php" class="button is-light">Cancel</a>
            </div>
            <div class="control">
                <button type="submit" class="button is-stormi-primary">
                    <span class="icon"><i class="fas fa-save"></i></span>
                    <span><?php echo $action === 'create' ? 'Create Artist' : 'Update Artist'; ?></span>
                </button>
            </div>
        </div>
    </form>
    
    <?php endif; ?>
</div>

<style>
.tab-content {
    display: none;
}
.tab-content.is-active {
    display: block;
}
.tabs li.is-active a {
    border-bottom-color: var(--stormi-medium);
    color: var(--stormi-dark);
}

/* Country dropdown styling */
#country-dropdown {
    box-shadow: 0 8px 16px rgba(0,0,0,0.1);
    border: 1px solid #dbdbdb;
    border-radius: 4px;
    max-height: 200px;
    overflow-y: auto;
    background: white;
    position: absolute;
    z-index: 1000;
    width: 100%;
    margin-top: 2px;
}

#country-dropdown .dropdown-item {
    padding: 0.5rem 0.75rem;
    cursor: pointer;
    border-bottom: 1px solid #f5f5f5;
    transition: background-color 0.2s ease;
}

#country-dropdown .dropdown-item:hover {
    background-color: var(--stormi-lightest) !important;
    color: var(--stormi-dark);
}

#country-dropdown .dropdown-item:last-child {
    border-bottom: none;
}
</style>

<script>
// Tab switching functionality
document.addEventListener('DOMContentLoaded', function() {
    const tabs = document.querySelectorAll('.tabs li');
    const tabContents = document.querySelectorAll('.tab-content');
    
    tabs.forEach(tab => {
        tab.addEventListener('click', function(e) {
            e.preventDefault();
            
            // Remove active class from all tabs and contents
            tabs.forEach(t => t.classList.remove('is-active'));
            tabContents.forEach(content => content.classList.remove('is-active'));
            
            // Add active class to clicked tab
            this.classList.add('is-active');
            
            // Show corresponding content
            const tabId = this.getAttribute('data-tab');
            const content = document.getElementById('tab-' + tabId);
            if (content) {
                content.classList.add('is-active');
            }
        });
    });
    
    // Notification close buttons
    document.querySelectorAll('.notification .delete').forEach(button => {
        button.addEventListener('click', function() {
            this.parentElement.remove();
        });
    });

    // File upload functionality
    const fileInput = document.querySelector('.file-input');
    if (fileInput) {
        fileInput.addEventListener('change', function() {
            const fileName = this.files[0] ? this.files[0].name : 'No file selected';
            const fileNameElement = this.parentElement.querySelector('.file-name');
            if (fileNameElement) {
                fileNameElement.textContent = fileName;
            }
        });
    }

    // Country search functionality
    const countries = [
        'Afghanistan', 'Albania', 'Algeria', 'Andorra', 'Angola', 'Argentina', 'Armenia', 'Australia', 'Austria', 'Azerbaijan',
        'Bahamas', 'Bahrain', 'Bangladesh', 'Barbados', 'Belarus', 'Belgium', 'Belize', 'Benin', 'Bhutan', 'Bolivia',
        'Bosnia and Herzegovina', 'Botswana', 'Brazil', 'Brunei', 'Bulgaria', 'Burkina Faso', 'Burundi',
        'Cambodia', 'Cameroon', 'Canada', 'Cape Verde', 'Central African Republic', 'Chad', 'Chile', 'China', 'Colombia',
        'Comoros', 'Congo', 'Costa Rica', 'Croatia', 'Cuba', 'Cyprus', 'Czech Republic',
        'Denmark', 'Djibouti', 'Dominica', 'Dominican Republic',
        'Ecuador', 'Egypt', 'El Salvador', 'Equatorial Guinea', 'Eritrea', 'Estonia', 'Ethiopia',
        'Fiji', 'Finland', 'France',
        'Gabon', 'Gambia', 'Georgia', 'Germany', 'Ghana', 'Greece', 'Grenada', 'Guatemala', 'Guinea', 'Guinea-Bissau', 'Guyana',
        'Haiti', 'Honduras', 'Hungary',
        'Iceland', 'India', 'Indonesia', 'Iran', 'Iraq', 'Ireland', 'Israel', 'Italy',
        'Jamaica', 'Japan', 'Jordan',
        'Kazakhstan', 'Kenya', 'Kiribati', 'Kuwait', 'Kyrgyzstan',
        'Laos', 'Latvia', 'Lebanon', 'Lesotho', 'Liberia', 'Libya', 'Liechtenstein', 'Lithuania', 'Luxembourg',
        'Madagascar', 'Malawi', 'Malaysia', 'Maldives', 'Mali', 'Malta', 'Marshall Islands', 'Mauritania', 'Mauritius',
        'Mexico', 'Micronesia', 'Moldova', 'Monaco', 'Mongolia', 'Montenegro', 'Morocco', 'Mozambique', 'Myanmar',
        'Namibia', 'Nauru', 'Nepal', 'Netherlands', 'New Zealand', 'Nicaragua', 'Niger', 'Nigeria', 'North Korea', 'North Macedonia', 'Norway',
        'Oman',
        'Pakistan', 'Palau', 'Palestine', 'Panama', 'Papua New Guinea', 'Paraguay', 'Peru', 'Philippines', 'Poland', 'Portugal',
        'Qatar',
        'Romania', 'Russia', 'Rwanda',
        'Saint Kitts and Nevis', 'Saint Lucia', 'Saint Vincent and the Grenadines', 'Samoa', 'San Marino', 'Sao Tome and Principe',
        'Saudi Arabia', 'Senegal', 'Serbia', 'Seychelles', 'Sierra Leone', 'Singapore', 'Slovakia', 'Slovenia', 'Solomon Islands',
        'Somalia', 'South Africa', 'South Korea', 'South Sudan', 'Spain', 'Sri Lanka', 'Sudan', 'Suriname', 'Sweden', 'Switzerland', 'Syria',
        'Taiwan', 'Tajikistan', 'Tanzania', 'Thailand', 'Timor-Leste', 'Togo', 'Tonga', 'Trinidad and Tobago', 'Tunisia', 'Turkey', 'Turkmenistan', 'Tuvalu',
        'Uganda', 'Ukraine', 'United Arab Emirates', 'United Kingdom', 'United States', 'Uruguay', 'Uzbekistan',
        'Vanuatu', 'Vatican City', 'Venezuela', 'Vietnam',
        'Yemen',
        'Zambia', 'Zimbabwe'
    ];

    const countryInput = document.getElementById('country-input');
    const countryDropdown = document.getElementById('country-dropdown');

    if (countryInput && countryDropdown) {
        countryInput.addEventListener('input', function() {
            const query = this.value.toLowerCase();
            const filtered = countries.filter(country =>
                country.toLowerCase().includes(query)
            );

            if (query.length > 0 && filtered.length > 0) {
                countryDropdown.innerHTML = filtered.map(country =>
                    `<div class="dropdown-item" style="padding: 0.5rem; cursor: pointer;" data-country="${country}">${country}</div>`
                ).join('');
                countryDropdown.style.display = 'block';
            } else {
                countryDropdown.style.display = 'none';
            }
        });

        countryDropdown.addEventListener('click', function(e) {
            if (e.target.classList.contains('dropdown-item')) {
                countryInput.value = e.target.getAttribute('data-country');
                countryDropdown.style.display = 'none';
            }
        });

        // Hide dropdown when clicking outside
        document.addEventListener('click', function(e) {
            if (!countryInput.contains(e.target) && !countryDropdown.contains(e.target)) {
                countryDropdown.style.display = 'none';
            }
        });

        // Add hover effects
        countryDropdown.addEventListener('mouseover', function(e) {
            if (e.target.classList.contains('dropdown-item')) {
                e.target.style.backgroundColor = '#f5f5f5';
            }
        });

        countryDropdown.addEventListener('mouseout', function(e) {
            if (e.target.classList.contains('dropdown-item')) {
                e.target.style.backgroundColor = '';
            }
        });
    }
});
</script>

<?php include 'includes/admin_footer.php'; ?>
