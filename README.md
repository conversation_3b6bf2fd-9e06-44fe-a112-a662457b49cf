# Artist Portfolio Management System

A comprehensive web-based system for managing artist portfolios with QR code generation, analytics tracking, and mobile-responsive design. Perfect for galleries, art organizations, or individual artists who want to showcase their work professionally.

## 🎨 Features

- **Mobile-Responsive Design**: Optimized for both mobile and desktop viewing using Bulma CSS framework
- **Artist Portfolio Pages**: Complete artist profiles with photos, bio, social media links, and galleries
- **QR Code Generation**: Automatic QR code creation with tracking and downloadable formats
- **Short URL System**: Memorable short URLs for easy sharing
- **Analytics Tracking**: Track page views, QR scans, and visitor statistics
- **Admin Dashboard**: Full content management system for creating and managing artist pages
- **Social Media Integration**: Support for Instagram, Facebook, YouTube, Twitter, LinkedIn, and websites
- **Gallery Management**: Upload and organize artwork with image optimization
- **SEO Optimized**: Built-in SEO features with meta tags and structured data

## 🚀 Installation

### Requirements

- **PHP**: 7.4 or higher
- **MySQL**: 5.7+ or MariaDB 10.2+
- **Web Server**: Apache or Nginx
- **PHP Extensions**: PDO, GD, cURL
- **Storage**: Write permissions for uploads directory

### Quick Install on cPanel

1. **Upload Files**: Extract all files to your domain's public_html directory
2. **Create Database**: Create a MySQL database through cPanel
3. **Run Installer**: Visit `yourdomain.com/install/install.php`
4. **Follow Wizard**: Complete the 6-step installation process
5. **Security**: Delete the `install` directory after completion

### Manual Installation

1. **Database Setup**:
   ```sql
   -- Import the database schema
   mysql -u username -p database_name < install/database.sql
   ```

2. **Configuration**:
   ```php
   // Edit config/config.php with your settings
   define('DB_HOST', 'localhost');
   define('DB_NAME', 'your_database');
   define('DB_USER', 'your_username');
   define('DB_PASS', 'your_password');
   define('SITE_URL', 'https://yourdomain.com');
   ```

3. **Permissions**:
   ```bash
   chmod 755 uploads/
   chmod 755 qr_codes/
   chmod 755 assets/images/
   ```

## 📁 Project Structure

```
artist-portfolio/
├── admin/                  # Admin panel files
├── assets/                 # CSS, JS, and images
│   ├── css/
│   │   └── style.css      # Main stylesheet with Bulma
│   └── js/
│       └── app.js         # Main JavaScript functionality
├── classes/               # PHP classes
├── config/                # Configuration files
│   ├── config.php         # Main configuration
│   └── database.php       # Database connection class
├── includes/              # Shared includes
│   ├── header.php         # HTML header template
│   └── footer.php         # HTML footer template
├── install/               # Installation files
│   ├── install.php        # Installation wizard
│   └── database.sql       # Database schema
├── public/                # Public pages
│   └── home.php           # Homepage
├── uploads/               # File uploads directory
├── qr_codes/             # Generated QR codes
└── index.php             # Main entry point
```

## 🎯 Current Status

### ✅ Completed Features

1. **Project Foundation**:
   - ✅ Bulma CSS framework integration
   - ✅ Responsive design system
   - ✅ JavaScript foundation with utilities
   - ✅ PHP configuration system
   - ✅ Database connection class
   - ✅ HTML template system (header/footer)

2. **Database Schema**:
   - ✅ Complete MySQL schema design
   - ✅ Artists table with all required fields
   - ✅ Gallery management tables
   - ✅ Short URLs and QR codes tables
   - ✅ Analytics tracking tables
   - ✅ Admin users and settings tables

3. **Installation System**:
   - ✅ Web-based installation wizard
   - ✅ Database setup automation
   - ✅ Admin user creation
   - ✅ Security configurations

### 🚧 In Progress

- Database schema implementation and testing

### 📋 Next Steps

1. **Authentication System**: Admin login and session management
2. **Admin Dashboard**: Main dashboard interface
3. **Artist Management**: CRUD operations for artist pages
4. **URL & QR Generation**: Short URL and QR code systems
5. **Analytics**: Tracking and reporting features
6. **Public Pages**: Mobile-responsive artist portfolio pages
7. **File Management**: Image upload and optimization

## 🛠 Technology Stack

- **Frontend**: Bulma CSS, Font Awesome, Vanilla JavaScript
- **Backend**: PHP 7.4+, MySQL/MariaDB
- **Architecture**: MVC-inspired structure with clean separation
- **Security**: PDO prepared statements, password hashing, CSRF protection
- **Performance**: Optimized queries, image compression, caching headers

## 🎨 Design Philosophy

- **Mobile-First**: Every component designed for mobile, enhanced for desktop
- **Accessibility**: WCAG 2.1 compliant with proper ARIA labels
- **Performance**: Optimized loading times and minimal dependencies
- **User Experience**: Intuitive interfaces for both admins and visitors
- **Scalability**: Built to handle hundreds of artists and thousands of visitors

## 📱 Mobile Optimization

- Responsive grid layouts using Bulma's column system
- Touch-friendly navigation and buttons
- Optimized image loading and compression
- Fast loading times on mobile networks
- Progressive Web App (PWA) capabilities

## 🔒 Security Features

- SQL injection prevention with PDO prepared statements
- XSS protection with proper output escaping
- CSRF token validation
- Secure file upload handling
- Session security and timeout management
- Input validation and sanitization

## 📊 Analytics Features

- Page view tracking
- QR code scan analytics
- Visitor geographic data
- Device and browser statistics
- Social media click tracking
- Custom event tracking

## 🤝 Contributing

This is a custom artist portfolio management system. For support or customization requests, please contact the development team.

## 📄 License

Proprietary software. All rights reserved.

## 🆘 Support

For installation help or technical support:
- Check the installation wizard at `/install/install.php`
- Review the database requirements
- Ensure proper file permissions
- Contact your hosting provider for server-specific issues

---

**Built with ❤️ for artists and galleries worldwide**
