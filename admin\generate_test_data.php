<?php
/**
 * Generate Test Analytics Data
 * Creates sample data for testing advanced analytics features
 */

require_once '../config/config.php';
require_once '../config/database.php';
require_once 'includes/auth_check.php';

$db = Database::getInstance();

echo "<h1>Generate Test Analytics Data</h1>";

try {
    // Generate test user journeys
    echo "<h2>Generating User Journey Data...</h2>";
    for ($i = 0; $i < 20; $i++) {
        $session_id = 'test_session_' . $i;
        $visitor_hash = hash('sha256', 'test_visitor_' . $i . date('Y-m-d'));
        $artist_id = rand(1, 10); // Assuming you have artists with IDs 1-10
        
        $sql = "INSERT INTO analytics_user_journeys 
                (session_id, visitor_hash, page_url, page_title, artist_id, 
                 entry_time, exit_time, time_spent, scroll_depth, interactions_count) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        
        $entry_time = date('Y-m-d H:i:s', strtotime('-' . rand(1, 30) . ' days'));
        $time_spent = rand(30, 300); // 30 seconds to 5 minutes
        $exit_time = date('Y-m-d H:i:s', strtotime($entry_time . ' +' . $time_spent . ' seconds'));
        
        $db->query($sql, [
            $session_id,
            $visitor_hash,
            'index.php?page=artist&slug=test-artist-' . $artist_id,
            'Test Artist ' . $artist_id,
            $artist_id,
            $entry_time,
            $exit_time,
            $time_spent,
            rand(20, 100), // scroll depth percentage
            rand(1, 15) // interactions count
        ]);
    }
    echo "✅ Generated 20 user journey records<br>";
    
    // Generate test gallery interactions
    echo "<h2>Generating Gallery Interaction Data...</h2>";
    $interaction_types = ['view', 'click', 'modal_open', 'modal_close', 'navigation'];
    for ($i = 0; $i < 50; $i++) {
        $session_id = 'test_session_' . rand(0, 19);
        $visitor_hash = hash('sha256', 'test_visitor_' . rand(0, 19) . date('Y-m-d'));
        $artist_id = rand(1, 10);
        
        $sql = "INSERT INTO analytics_gallery_interactions 
                (session_id, visitor_hash, artist_id, gallery_image_id, interaction_type, 
                 x_coordinate, y_coordinate, timestamp) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)";
        
        $timestamp = date('Y-m-d H:i:s', strtotime('-' . rand(1, 30) . ' days'));
        
        $db->query($sql, [
            $session_id,
            $visitor_hash,
            $artist_id,
            rand(1, 5), // gallery image ID
            $interaction_types[array_rand($interaction_types)],
            rand(100, 800), // x coordinate
            rand(100, 600), // y coordinate
            $timestamp
        ]);
    }
    echo "✅ Generated 50 gallery interaction records<br>";
    
    // Generate test social clicks
    echo "<h2>Generating Social Media Click Data...</h2>";
    $social_platforms = ['facebook', 'instagram', 'twitter', 'linkedin', 'youtube'];
    for ($i = 0; $i < 25; $i++) {
        $session_id = 'test_session_' . rand(0, 19);
        $visitor_hash = hash('sha256', 'test_visitor_' . rand(0, 19) . date('Y-m-d'));
        $artist_id = rand(1, 10);
        $platform = $social_platforms[array_rand($social_platforms)];
        
        $sql = "INSERT INTO analytics_social_clicks 
                (session_id, visitor_hash, artist_id, social_platform, click_url, timestamp) 
                VALUES (?, ?, ?, ?, ?, ?)";
        
        $timestamp = date('Y-m-d H:i:s', strtotime('-' . rand(1, 30) . ' days'));
        
        $db->query($sql, [
            $session_id,
            $visitor_hash,
            $artist_id,
            $platform,
            'https://' . $platform . '.com/test-artist-' . $artist_id,
            $timestamp
        ]);
    }
    echo "✅ Generated 25 social media click records<br>";
    
    // Generate test conversion funnel data
    echo "<h2>Generating Conversion Funnel Data...</h2>";
    $funnel_steps = ['landing', 'profile_view', 'gallery_view', 'social_click', 'contact_attempt'];
    for ($i = 0; $i < 40; $i++) {
        $session_id = 'test_session_' . rand(0, 19);
        $visitor_hash = hash('sha256', 'test_visitor_' . rand(0, 19) . date('Y-m-d'));
        $artist_id = rand(1, 10);
        
        $sql = "INSERT INTO analytics_conversion_funnels 
                (session_id, visitor_hash, artist_id, funnel_step, timestamp) 
                VALUES (?, ?, ?, ?, ?)";
        
        $timestamp = date('Y-m-d H:i:s', strtotime('-' . rand(1, 30) . ' days'));
        
        $db->query($sql, [
            $session_id,
            $visitor_hash,
            $artist_id,
            $funnel_steps[array_rand($funnel_steps)],
            $timestamp
        ]);
    }
    echo "✅ Generated 40 conversion funnel records<br>";
    
    // Generate test heatmap data
    echo "<h2>Generating Heatmap Data...</h2>";
    $interaction_types = ['click', 'hover', 'scroll'];
    $elements = ['.masonry-item', '.social-link', '.artist-name', '.gallery-section', '.artist-bio'];
    
    for ($i = 0; $i < 100; $i++) {
        $session_id = 'test_session_' . rand(0, 19);
        $visitor_hash = hash('sha256', 'test_visitor_' . rand(0, 19) . date('Y-m-d'));
        $artist_id = rand(1, 10);
        
        $sql = "INSERT INTO analytics_heatmap_data 
                (session_id, visitor_hash, page_url, artist_id, element_selector, 
                 interaction_type, x_coordinate, y_coordinate, viewport_width, viewport_height, timestamp) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        
        $timestamp = date('Y-m-d H:i:s', strtotime('-' . rand(1, 30) . ' days'));
        
        $db->query($sql, [
            $session_id,
            $visitor_hash,
            'index.php?page=artist&slug=test-artist-' . $artist_id,
            $artist_id,
            $elements[array_rand($elements)],
            $interaction_types[array_rand($interaction_types)],
            rand(100, 1200), // x coordinate
            rand(100, 800), // y coordinate
            rand(1024, 1920), // viewport width
            rand(768, 1080), // viewport height
            $timestamp
        ]);
    }
    echo "✅ Generated 100 heatmap data points<br>";
    
    echo "<h2>✅ Test Data Generation Complete!</h2>";
    echo "<p><strong>Summary:</strong></p>";
    echo "<ul>";
    echo "<li>20 User Journey records</li>";
    echo "<li>50 Gallery Interaction records</li>";
    echo "<li>25 Social Media Click records</li>";
    echo "<li>40 Conversion Funnel records</li>";
    echo "<li>100 Heatmap data points</li>";
    echo "</ul>";
    
    echo "<p><a href='analytics.php' class='button is-primary'>View Analytics Dashboard</a></p>";
    echo "<p><a href='heatmap.php' class='button is-info'>View Heatmap</a></p>";
    
} catch (Exception $e) {
    echo "<h2>❌ Error: " . $e->getMessage() . "</h2>";
}
?>
