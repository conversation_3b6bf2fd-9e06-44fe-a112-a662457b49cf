/**
 * Artist Portfolio Management System - Main JavaScript
 */

// Global App Object
const ArtistPortfolio = {
    // Configuration
    config: {
        apiUrl: window.location.origin,
        uploadMaxSize: 5 * 1024 * 1024, // 5MB
        allowedTypes: ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp']
    },

    // Initialize the application
    init() {
        this.bindEvents();
        this.initComponents();
        console.log('Artist Portfolio System initialized');
    },

    // Bind global events
    bindEvents() {
        // Mobile menu toggle
        document.addEventListener('DOMContentLoaded', () => {
            const burger = document.querySelector('.navbar-burger');
            const menu = document.querySelector('.navbar-menu');
            
            if (burger && menu) {
                burger.addEventListener('click', () => {
                    burger.classList.toggle('is-active');
                    menu.classList.toggle('is-active');
                });
            }
        });

        // Form validation
        document.addEventListener('submit', (e) => {
            if (e.target.classList.contains('validate-form')) {
                if (!this.validateForm(e.target)) {
                    e.preventDefault();
                }
            }
        });

        // File upload preview
        document.addEventListener('change', (e) => {
            if (e.target.type === 'file' && e.target.classList.contains('image-upload')) {
                this.previewImage(e.target);
            }
        });

        // Confirm delete actions
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('confirm-delete')) {
                if (!confirm('Are you sure you want to delete this item?')) {
                    e.preventDefault();
                }
            }
        });
    },

    // Initialize components
    initComponents() {
        this.initTooltips();
        this.initModals();
        this.initTabs();
        this.initDropdowns();
    },

    // Initialize tooltips
    initTooltips() {
        const tooltips = document.querySelectorAll('[data-tooltip]');
        tooltips.forEach(element => {
            element.addEventListener('mouseenter', (e) => {
                this.showTooltip(e.target);
            });
            element.addEventListener('mouseleave', (e) => {
                this.hideTooltip(e.target);
            });
        });
    },

    // Initialize modals
    initModals() {
        const modalTriggers = document.querySelectorAll('[data-modal]');
        modalTriggers.forEach(trigger => {
            trigger.addEventListener('click', (e) => {
                e.preventDefault();
                const modalId = trigger.getAttribute('data-modal');
                this.openModal(modalId);
            });
        });

        const modalCloses = document.querySelectorAll('.modal-close, .modal-background');
        modalCloses.forEach(close => {
            close.addEventListener('click', (e) => {
                this.closeModal(e.target.closest('.modal'));
            });
        });
    },

    // Initialize tabs
    initTabs() {
        const tabContainers = document.querySelectorAll('.tabs');
        tabContainers.forEach(container => {
            const tabs = container.querySelectorAll('.tab-link');
            tabs.forEach(tab => {
                tab.addEventListener('click', (e) => {
                    e.preventDefault();
                    this.switchTab(tab);
                });
            });
        });
    },

    // Initialize dropdowns
    initDropdowns() {
        const dropdowns = document.querySelectorAll('.dropdown');
        dropdowns.forEach(dropdown => {
            const trigger = dropdown.querySelector('.dropdown-trigger');
            if (trigger) {
                trigger.addEventListener('click', (e) => {
                    e.preventDefault();
                    dropdown.classList.toggle('is-active');
                });
            }
        });

        // Close dropdowns when clicking outside
        document.addEventListener('click', (e) => {
            if (!e.target.closest('.dropdown')) {
                document.querySelectorAll('.dropdown.is-active').forEach(dropdown => {
                    dropdown.classList.remove('is-active');
                });
            }
        });
    },

    // Form validation
    validateForm(form) {
        let isValid = true;
        const requiredFields = form.querySelectorAll('[required]');
        
        requiredFields.forEach(field => {
            if (!field.value.trim()) {
                this.showFieldError(field, 'This field is required');
                isValid = false;
            } else {
                this.clearFieldError(field);
            }
        });

        // Email validation
        const emailFields = form.querySelectorAll('input[type="email"]');
        emailFields.forEach(field => {
            if (field.value && !this.isValidEmail(field.value)) {
                this.showFieldError(field, 'Please enter a valid email address');
                isValid = false;
            }
        });

        // URL validation
        const urlFields = form.querySelectorAll('input[type="url"]');
        urlFields.forEach(field => {
            if (field.value && !this.isValidUrl(field.value)) {
                this.showFieldError(field, 'Please enter a valid URL');
                isValid = false;
            }
        });

        return isValid;
    },

    // Show field error
    showFieldError(field, message) {
        this.clearFieldError(field);
        field.classList.add('is-danger');
        
        const errorDiv = document.createElement('p');
        errorDiv.className = 'help is-danger field-error';
        errorDiv.textContent = message;
        
        field.parentNode.appendChild(errorDiv);
    },

    // Clear field error
    clearFieldError(field) {
        field.classList.remove('is-danger');
        const existingError = field.parentNode.querySelector('.field-error');
        if (existingError) {
            existingError.remove();
        }
    },

    // Email validation
    isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    },

    // URL validation
    isValidUrl(url) {
        try {
            new URL(url);
            return true;
        } catch {
            return false;
        }
    },

    // Image preview
    previewImage(input) {
        if (input.files && input.files[0]) {
            const file = input.files[0];
            
            // Validate file type
            if (!this.config.allowedTypes.includes(file.type)) {
                this.showNotification('Please select a valid image file', 'error');
                input.value = '';
                return;
            }
            
            // Validate file size
            if (file.size > this.config.uploadMaxSize) {
                this.showNotification('File size must be less than 5MB', 'error');
                input.value = '';
                return;
            }
            
            const reader = new FileReader();
            reader.onload = (e) => {
                const previewId = input.getAttribute('data-preview');
                const preview = document.getElementById(previewId);
                if (preview) {
                    preview.src = e.target.result;
                    preview.style.display = 'block';
                }
            };
            reader.readAsDataURL(file);
        }
    },

    // Modal functions
    openModal(modalId) {
        const modal = document.getElementById(modalId);
        if (modal) {
            modal.classList.add('is-active');
            document.body.classList.add('modal-open');
        }
    },

    closeModal(modal) {
        if (modal) {
            modal.classList.remove('is-active');
            document.body.classList.remove('modal-open');
        }
    },

    // Tab switching
    switchTab(activeTab) {
        const container = activeTab.closest('.tabs');
        const targetId = activeTab.getAttribute('href').substring(1);
        
        // Remove active class from all tabs
        container.querySelectorAll('.tab-link').forEach(tab => {
            tab.parentElement.classList.remove('is-active');
        });
        
        // Add active class to clicked tab
        activeTab.parentElement.classList.add('is-active');
        
        // Hide all tab content
        const tabContents = document.querySelectorAll('.tab-content');
        tabContents.forEach(content => {
            content.style.display = 'none';
        });
        
        // Show target tab content
        const targetContent = document.getElementById(targetId);
        if (targetContent) {
            targetContent.style.display = 'block';
        }
    },

    // Notification system
    showNotification(message, type = 'info', duration = 5000) {
        const notification = document.createElement('div');
        notification.className = `notification is-${type} notification-toast`;
        notification.innerHTML = `
            <button class="delete"></button>
            ${message}
        `;
        
        document.body.appendChild(notification);
        
        // Position notification
        notification.style.position = 'fixed';
        notification.style.top = '20px';
        notification.style.right = '20px';
        notification.style.zIndex = '9999';
        notification.style.maxWidth = '400px';
        
        // Close button
        notification.querySelector('.delete').addEventListener('click', () => {
            notification.remove();
        });
        
        // Auto remove
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, duration);
    },

    // AJAX helper
    ajax(url, options = {}) {
        const defaults = {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        };
        
        const config = { ...defaults, ...options };
        
        return fetch(url, config)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .catch(error => {
                console.error('AJAX Error:', error);
                this.showNotification('An error occurred. Please try again.', 'error');
                throw error;
            });
    },

    // Utility functions
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },

    // Copy to clipboard
    copyToClipboard(text) {
        navigator.clipboard.writeText(text).then(() => {
            this.showNotification('Copied to clipboard!', 'success');
        }).catch(() => {
            this.showNotification('Failed to copy to clipboard', 'error');
        });
    }
};

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    ArtistPortfolio.init();
});

// Export for use in other scripts
window.ArtistPortfolio = ArtistPortfolio;
