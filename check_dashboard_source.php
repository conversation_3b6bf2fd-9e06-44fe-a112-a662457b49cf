<?php
echo "<h2>Dashboard Source Code Check</h2>";

// Get the dashboard HTML
$dashboard_url = "http://localhost/Thanking Card/admin/index.php";
$html = file_get_contents($dashboard_url);

if ($html) {
    echo "<h3>1. Check if admin footer is included</h3>";
    if (strpos($html, 'Confirm delete actions') !== false) {
        echo "<p style='color: green;'>✅ Admin footer JavaScript is included</p>";
    } else {
        echo "<p style='color: red;'>❌ Admin footer JavaScript is NOT included</p>";
    }
    
    echo "<h3>2. Check for delete buttons</h3>";
    if (strpos($html, 'confirm-delete') !== false) {
        echo "<p style='color: green;'>✅ Delete buttons with confirm-delete class found</p>";
    } else {
        echo "<p style='color: red;'>❌ No delete buttons found</p>";
    }
    
    echo "<h3>3. Check for JavaScript errors</h3>";
    if (strpos($html, 'error') !== false || strpos($html, 'Error') !== false) {
        echo "<p style='color: orange;'>⚠️ Possible errors found in HTML</p>";
    } else {
        echo "<p style='color: green;'>✅ No obvious errors in HTML</p>";
    }
    
    echo "<h3>4. Extract delete button HTML</h3>";
    $pattern = '/class="[^"]*confirm-delete[^"]*"[^>]*data-url="[^"]*"/';
    preg_match_all($pattern, $html, $matches);
    
    if (!empty($matches[0])) {
        echo "<p style='color: green;'>✅ Found " . count($matches[0]) . " delete button(s):</p>";
        foreach ($matches[0] as $match) {
            echo "<code>" . htmlspecialchars($match) . "</code><br>";
        }
    } else {
        echo "<p style='color: red;'>❌ No delete buttons found in HTML</p>";
    }
    
    echo "<h3>5. Check JavaScript loading</h3>";
    if (strpos($html, 'app.js') !== false) {
        echo "<p style='color: green;'>✅ app.js is referenced</p>";
    } else {
        echo "<p style='color: red;'>❌ app.js is NOT referenced</p>";
    }
    
    echo "<h3>6. Extract JavaScript section</h3>";
    $js_start = strpos($html, 'Confirm delete actions');
    if ($js_start !== false) {
        $js_section = substr($html, $js_start, 500);
        echo "<pre style='background: #f5f5f5; padding: 10px; border-radius: 5px;'>";
        echo htmlspecialchars($js_section);
        echo "</pre>";
    } else {
        echo "<p style='color: red;'>❌ Delete handler JavaScript not found</p>";
    }
    
    echo "<h3>7. Manual Test</h3>";
    echo "<p>Go to the dashboard and:</p>";
    echo "<ol>";
    echo "<li>Open browser developer tools (F12)</li>";
    echo "<li>Go to Console tab</li>";
    echo "<li>Look for any error messages</li>";
    echo "<li>Try typing: <code>document.querySelector('.confirm-delete')</code></li>";
    echo "<li>Try clicking a delete button and watch the console</li>";
    echo "</ol>";
    
} else {
    echo "<p style='color: red;'>❌ Could not fetch dashboard HTML</p>";
}

echo "<p><a href='admin/index.php'>Go to Dashboard</a></p>";
?>
