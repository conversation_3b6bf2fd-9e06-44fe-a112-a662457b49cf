<!DOCTYPE html>
<html>
<head>
    <title>Simple Delete Test</title>
</head>
<body>
    <h2>Simple Delete Button Test</h2>
    
    <p>This is a completely isolated test to see if basic JavaScript works.</p>
    
    <h3>Test 1: Basic Alert</h3>
    <button onclick="alert('Basic JavaScript works!')">Test Basic JS</button>
    
    <h3>Test 2: Simple Delete with onclick</h3>
    <button onclick="if(confirm('Delete this item?')) { alert('Would delete now!'); }">Test Delete (onclick)</button>
    
    <h3>Test 3: Event Listener Delete</h3>
    <button id="delete-test" data-url="admin/artists.php?action=delete&id=1">Test Delete (Event Listener)</button>
    
    <h3>Test 4: Class-based Delete (like dashboard)</h3>
    <button class="confirm-delete" data-url="admin/artists.php?action=delete&id=1">Test Delete (Class-based)</button>
    
    <h3>Test 5: Nested Element Delete (like dashboard with icon)</h3>
    <button class="confirm-delete" data-url="admin/artists.php?action=delete&id=1">
        <span class="icon"><i class="fas fa-trash"></i></span>
        Test Delete (Nested)
    </button>
    
    <h3>Instructions:</h3>
    <ol>
        <li>Open browser developer tools (F12)</li>
        <li>Go to Console tab</li>
        <li>Try each button above</li>
        <li>Check console for any messages or errors</li>
    </ol>
    
    <p><a href="admin/index.php">Go to Dashboard</a> | <a href="admin/qr_codes.php">Go to QR Codes</a></p>

<script>
console.log('=== SIMPLE DELETE TEST SCRIPT LOADED ===');

// Test 3: Event listener for specific ID
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM loaded');
    
    const deleteTest = document.getElementById('delete-test');
    if (deleteTest) {
        deleteTest.addEventListener('click', function() {
            console.log('Delete test button clicked');
            const url = this.getAttribute('data-url');
            if (confirm('Delete this item? URL: ' + url)) {
                alert('Would redirect to: ' + url);
            }
        });
    }
});

// Test 4 & 5: Class-based delete handler (exact copy from admin footer)
document.addEventListener('click', (e) => {
    console.log('Global click detected on:', e.target);
    console.log('Target classes:', e.target.className);
    console.log('Closest confirm-delete:', e.target.closest('.confirm-delete'));
    
    if (e.target.classList.contains('confirm-delete') || 
        e.target.closest('.confirm-delete')) {
        
        console.log('CONFIRM DELETE DETECTED!');
        e.preventDefault();
        
        const deleteButton = e.target.closest('.confirm-delete') || e.target;
        const deleteUrl = deleteButton.getAttribute('data-url');
        
        console.log('Delete button:', deleteButton);
        console.log('Delete URL:', deleteUrl);
        
        if (deleteUrl && confirm('Are you sure you want to delete this item? This action cannot be undone.')) {
            console.log('User confirmed, would redirect to:', deleteUrl);
            alert('SUCCESS! Would redirect to: ' + deleteUrl + '\n\nThis means the delete functionality is working!');
            // Uncomment to actually redirect:
            // window.location.href = deleteUrl;
        } else {
            console.log('User cancelled or no URL');
        }
        return false;
    }
});

console.log('=== ALL TEST HANDLERS LOADED ===');
</script>

</body>
</html>
