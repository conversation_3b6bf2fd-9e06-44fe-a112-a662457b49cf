<?php
/**
 * Language Management Class
 * Handles multi-language functionality for the frontend
 */

class Language {
    private $db;
    private $current_language;
    private $available_languages;
    
    public function __construct() {
        $this->db = Database::getInstance();
        $this->loadAvailableLanguages();
        $this->setCurrentLanguage();
    }
    
    /**
     * Load available languages from database
     */
    private function loadAvailableLanguages() {
        try {
            $stmt = $this->db->query("SELECT * FROM languages WHERE is_active = 1 ORDER BY sort_order ASC");
            $this->available_languages = $stmt->fetchAll();
        } catch (Exception $e) {
            // Fallback to English only if database error
            $this->available_languages = [
                ['code' => 'en', 'name' => 'English', 'native_name' => 'English', 'is_default' => 1]
            ];
        }
    }
    
    /**
     * Set current language based on URL parameter, session, cookie, or default
     */
    private function setCurrentLanguage() {
        // Check URL parameter first (highest priority)
        if (isset($_GET['lang']) && $this->isValidLanguage($_GET['lang'])) {
            $this->current_language = $_GET['lang'];
            $this->saveLanguagePreference($_GET['lang']);
        }
        // Check session
        elseif (isset($_SESSION['language']) && $this->isValidLanguage($_SESSION['language'])) {
            $this->current_language = $_SESSION['language'];
        }
        // Check cookie
        elseif (isset($_COOKIE['language']) && $this->isValidLanguage($_COOKIE['language'])) {
            $this->current_language = $_COOKIE['language'];
        }
        // Use default language
        else {
            $default = $this->getDefaultLanguage();
            $this->current_language = $default['code'];
        }
    }
    
    /**
     * Get current language code
     */
    public function getCurrentLanguage() {
        return $this->current_language;
    }
    
    /**
     * Get all available languages
     */
    public function getAvailableLanguages() {
        return $this->available_languages;
    }
    
    /**
     * Get default language
     */
    public function getDefaultLanguage() {
        foreach ($this->available_languages as $lang) {
            if ($lang['is_default']) {
                return $lang;
            }
        }
        // Fallback to first language
        return $this->available_languages[0] ?? ['code' => 'en', 'name' => 'English', 'native_name' => 'English'];
    }
    
    /**
     * Check if language code is valid
     */
    public function isValidLanguage($code) {
        foreach ($this->available_languages as $lang) {
            if ($lang['code'] === $code) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * Switch to a different language
     */
    public function switchLanguage($language_code) {
        if ($this->isValidLanguage($language_code)) {
            $this->current_language = $language_code;
            $this->saveLanguagePreference($language_code);
            return true;
        }
        return false;
    }
    
    /**
     * Save language preference in session and cookie
     */
    private function saveLanguagePreference($language_code) {
        $_SESSION['language'] = $language_code;
        setcookie('language', $language_code, time() + (86400 * 30), '/'); // 30 days
    }
    
    /**
     * Get artist data with translations
     */
    public function getArtistWithTranslation($artist_id, $language_code = null) {
        if (!$language_code) {
            $language_code = $this->current_language;
        }
        
        try {
            // Get base artist data
            $stmt = $this->db->query("SELECT * FROM artists WHERE id = ? AND is_active = 1", [$artist_id]);
            $artist = $stmt->fetch();
            
            if (!$artist) {
                return null;
            }
            
            // Get translation for current language
            $stmt = $this->db->query("SELECT * FROM artist_translations WHERE artist_id = ? AND language_code = ?",
                                   [$artist_id, $language_code]);
            $translation = $stmt->fetch();

            // Get English translation as fallback
            $english_translation = null;
            if ($language_code !== 'en') {
                $stmt = $this->db->query("SELECT * FROM artist_translations WHERE artist_id = ? AND language_code = 'en'",
                                       [$artist_id]);
                $english_translation = $stmt->fetch();
            }

            // If no translation found, use English as fallback
            if (!$translation && $english_translation) {
                $translation = $english_translation;
            }

            // Merge translation data with artist data, using English fallback for empty fields
            if ($translation) {
                $artist['name'] = $this->getFieldWithFallback($translation['name'], $english_translation['name'] ?? '', $artist['name']);
                $artist['bio'] = $this->getFieldWithFallback($translation['bio'], $english_translation['bio'] ?? '');
                $artist['description'] = $this->getFieldWithFallback($translation['description'], $english_translation['description'] ?? '');
                $artist['main_medium'] = $this->getFieldWithFallback($translation['main_medium'], $english_translation['main_medium'] ?? '');
                $artist['main_subjects'] = $this->getFieldWithFallback($translation['main_subjects'], $english_translation['main_subjects'] ?? '');
                $artist['education'] = $this->getFieldWithFallback($translation['education'], $english_translation['education'] ?? '');
                $artist['exhibitions'] = $this->getFieldWithFallback($translation['exhibitions'], $english_translation['exhibitions'] ?? '');
                $artist['awards'] = $this->getFieldWithFallback($translation['awards'], $english_translation['awards'] ?? '');
                $artist['artist_statement'] = $this->getFieldWithFallback($translation['artist_statement'], $english_translation['artist_statement'] ?? '');
            }
            
            return $artist;
            
        } catch (Exception $e) {
            error_log("Error getting artist with translation: " . $e->getMessage());
            return null;
        }
    }
    
    /**
     * Get artist by slug with translations
     */
    public function getArtistBySlugWithTranslation($slug, $language_code = null) {
        if (!$language_code) {
            $language_code = $this->current_language;
        }
        
        try {
            // Get base artist data by slug
            $stmt = $this->db->query("SELECT * FROM artists WHERE slug = ? AND is_active = 1", [$slug]);
            $artist = $stmt->fetch();
            
            if (!$artist) {
                return null;
            }
            
            return $this->getArtistWithTranslation($artist['id'], $language_code);
            
        } catch (Exception $e) {
            error_log("Error getting artist by slug with translation: " . $e->getMessage());
            return null;
        }
    }
    
    /**
     * Get local artists with translations
     */
    public function getLocalArtistsWithTranslations($limit = 6, $language_code = null) {
        if (!$language_code) {
            $language_code = $this->current_language;
        }

        try {
            // Get local artists (is_featured = 1 represents local artists)
            $stmt = $this->db->query("SELECT * FROM artists WHERE is_active = 1 AND is_featured = 1 ORDER BY sort_order ASC, created_at DESC LIMIT ?", [$limit]);
            $artists = $stmt->fetchAll();

            // Add translations to each artist
            foreach ($artists as &$artist) {
                $translated = $this->getArtistWithTranslation($artist['id'], $language_code);
                if ($translated) {
                    $artist = $translated;
                }
            }

            return $artists;

        } catch (Exception $e) {
            error_log("Error getting local artists with translations: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Get featured artists with translations (alias for backward compatibility)
     */
    public function getFeaturedArtistsWithTranslations($limit = 6, $language_code = null) {
        return $this->getLocalArtistsWithTranslations($limit, $language_code);
    }
    
    /**
     * Generate language selector HTML
     */
    public function generateLanguageSelector($current_url = null) {
        if (!$current_url) {
            $current_url = $_SERVER['REQUEST_URI'];
        }
        
        $html = '<div class="dropdown is-hoverable">';
        $html .= '<div class="dropdown-trigger">';
        $html .= '<button class="button is-small" aria-haspopup="true" aria-controls="language-menu">';
        
        // Current language flag/icon
        $current_lang = null;
        foreach ($this->available_languages as $lang) {
            if ($lang['code'] === $this->current_language) {
                $current_lang = $lang;
                break;
            }
        }
        
        $html .= '<span class="icon is-small">';
        $html .= '<i class="fas fa-globe"></i>';
        $html .= '</span>';
        $html .= '<span>' . htmlspecialchars($current_lang['native_name'] ?? 'Language') . '</span>';
        $html .= '<span class="icon is-small">';
        $html .= '<i class="fas fa-angle-down" aria-hidden="true"></i>';
        $html .= '</span>';
        $html .= '</button>';
        $html .= '</div>';
        
        $html .= '<div class="dropdown-menu" id="language-menu" role="menu">';
        $html .= '<div class="dropdown-content">';
        
        foreach ($this->available_languages as $lang) {
            $url = $this->addLanguageToUrl($current_url, $lang['code']);
            $is_current = $lang['code'] === $this->current_language;
            
            $html .= '<a href="' . htmlspecialchars($url) . '" class="dropdown-item' . ($is_current ? ' is-active' : '') . '">';
            $html .= '<span class="icon is-small">';
            $html .= '<i class="fas fa-' . ($lang['code'] === 'en' ? 'globe' : 'language') . '"></i>';
            $html .= '</span>';
            $html .= '<span>' . htmlspecialchars($lang['native_name']) . '</span>';
            if ($is_current) {
                $html .= '<span class="icon is-small has-text-success">';
                $html .= '<i class="fas fa-check"></i>';
                $html .= '</span>';
            }
            $html .= '</a>';
        }
        
        $html .= '</div>';
        $html .= '</div>';
        $html .= '</div>';
        
        return $html;
    }
    
    /**
     * Get field value with English fallback
     */
    private function getFieldWithFallback($primary_value, $fallback_value, $default_value = '') {
        // Return primary value if it's not empty
        if (!empty(trim($primary_value))) {
            return $primary_value;
        }

        // Return fallback value if it's not empty
        if (!empty(trim($fallback_value))) {
            return $fallback_value;
        }

        // Return default value as last resort
        return $default_value;
    }

    /**
     * Add language parameter to URL
     */
    private function addLanguageToUrl($url, $language_code) {
        $parsed = parse_url($url);
        $query = [];

        if (isset($parsed['query'])) {
            parse_str($parsed['query'], $query);
        }

        $query['lang'] = $language_code;

        $new_url = $parsed['path'] ?? '/';
        if (!empty($query)) {
            $new_url .= '?' . http_build_query($query);
        }

        return $new_url;
    }
}
?>
