<?php
/**
 * QR Code Management System
 */

require_once '../config/config.php';
require_once '../config/database.php';
require_once 'includes/auth_check.php';
require_once '../classes/QRCodeGenerator.php';

$db = Database::getInstance();
$qr_generator = new QRCodeGenerator();
$action = $_GET['action'] ?? 'list';
$qr_id = $_GET['id'] ?? null;

// Handle download action
if ($action === 'download' && $qr_id) {
    $qr_generator->downloadQRCode($qr_id);
    exit;
}

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if ($action === 'generate') {
        try {
            $db->beginTransaction();

            // Generate unique short code
            do {
                $short_code = strtoupper(substr(md5(uniqid()), 0, 8));
                $stmt = $db->query("SELECT id FROM qr_codes WHERE short_code = ?", [$short_code]);
            } while ($stmt->fetch());

            $short_url = SITE_URL . '/qr/' . $short_code;

            // Get admin user ID safely
            $admin_id = null;
            if (isset($_SESSION['admin_user']['id'])) {
                $admin_id = $_SESSION['admin_user']['id'];
            } else {
                // Try to get first admin user
                $admin_stmt = $db->query("SELECT id FROM admin_users LIMIT 1");
                $admin_user = $admin_stmt->fetch();
                if ($admin_user) {
                    $admin_id = $admin_user['id'];
                }
            }

            // Insert QR code record
            $title = $_POST['title'] ?? 'QR Code';
            $description = $_POST['description'] ?? '';

            $sql = "INSERT INTO qr_codes (short_code, short_url, title, description, created_by)
                    VALUES (?, ?, ?, ?, ?)";

            $db->query($sql, [
                $short_code,
                $short_url,
                $title,
                $description,
                $admin_id
            ]);

            $qr_id = $db->lastInsertId();
            $db->commit();

            header("Location: qr_codes.php?action=edit&id={$qr_id}&success=" . urlencode("QR code generated successfully!"));
            exit;

        } catch (Exception $e) {
            $db->rollback();
            $error_message = "Error generating QR code: " . $e->getMessage() . " | SQL: " . $sql ?? 'No SQL';
        }
    } elseif ($action === 'update' && $qr_id) {
        try {
            $sql = "UPDATE qr_codes SET target_url = ?, title = ?, description = ? WHERE id = ?";
            $db->query($sql, [
                $_POST['target_url'],
                $_POST['title'],
                $_POST['description'],
                $qr_id
            ]);
            
            header("Location: qr_codes.php?action=edit&id={$qr_id}&success=" . urlencode("QR code updated successfully!"));
            exit;
            
        } catch (Exception $e) {
            $error_message = "Error updating QR code: " . $e->getMessage();
        }
    } elseif ($action === 'map_artist' && $qr_id) {
        try {
            $artist_id = $_POST['artist_id'];

            // Create artist mapping
            $sql = "INSERT IGNORE INTO qr_artist_mappings (artist_id, qr_code_id, is_primary) VALUES (?, ?, 1)";
            $db->query($sql, [$artist_id, $qr_id]);

            header("Location: qr_codes.php?action=edit&id={$qr_id}&success=" . urlencode("Artist mapping created successfully!"));
            exit;

        } catch (Exception $e) {
            $error_message = "Error creating artist mapping: " . $e->getMessage();
        }
    }
}

// Get QR code data for editing
$qr_code = null;
if ($action === 'edit' && $qr_id) {
    $stmt = $db->query("SELECT * FROM qr_codes WHERE id = ?", [$qr_id]);
    $qr_code = $stmt->fetch();
}

// Get QR codes list
$qr_codes = [];
if ($action === 'list') {
    $stmt = $db->query("SELECT qr.*, 'Admin' as created_by_name
                       FROM qr_codes qr
                       ORDER BY qr.created_at DESC");
    $qr_codes = $stmt->fetchAll();
}

// Get artists for mapping dropdown
$artists = [];
$stmt = $db->query("SELECT a.id, a.name, a.slug, at.name as translated_name 
                   FROM artists a 
                   LEFT JOIN artist_translations at ON a.id = at.artist_id AND at.language_code = 'en'
                   WHERE a.is_active = 1 
                   ORDER BY COALESCE(at.name, a.name) ASC");
$artists = $stmt->fetchAll();

$page_title = $action === 'edit' ? 'Edit QR Code' : 
              ($action === 'generate' ? 'Generate QR Code' : 'QR Code Management');

include 'includes/admin_header.php';
?>

<div class="container is-fluid">
    <?php if (isset($_GET['success'])): ?>
    <div class="notification is-success is-light">
        <button class="delete"></button>
        <?php echo htmlspecialchars($_GET['success']); ?>
    </div>
    <?php endif; ?>
    
    <?php if (isset($error_message)): ?>
    <div class="notification is-danger is-light">
        <button class="delete"></button>
        <?php echo htmlspecialchars($error_message); ?>
    </div>
    <?php endif; ?>

    <?php if ($action === 'list'): ?>
    <!-- QR Codes List -->
    <div class="level mb-6">
        <div class="level-left">
            <div class="level-item">
                <div>
                    <h1 class="title is-3 mb-2 stormi-text-dark">QR Code Management</h1>
                    <p class="subtitle is-5 mb-0 has-text-grey-dark">Generate and manage QR codes with URL mappings</p>
                </div>
            </div>
        </div>
        <div class="level-right">
            <div class="level-item">
                <a href="qr_codes.php?action=generate" class="button is-stormi-primary">
                    <span class="icon"><i class="fas fa-qrcode"></i></span>
                    <span>Generate QR Code</span>
                </a>
            </div>
        </div>
    </div>

    <div class="box">
        <?php if ($qr_codes): ?>
        <div class="table-container">
            <table class="table is-fullwidth is-hoverable">
                <thead>
                    <tr>
                        <th>QR Code</th>
                        <th>Short URL</th>
                        <th>Target URL</th>
                        <th>Status</th>
                        <th>Scans</th>
                        <th>Created</th>
                        <th width="180">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($qr_codes as $qr): ?>
                    <tr>
                        <td>
                            <div class="media">
                                <div class="media-left">
                                    <div class="qr-preview" style="width: 40px; height: 40px; background: #f5f5f5; border-radius: 4px; display: flex; align-items: center; justify-content: center;">
                                        <i class="fas fa-qrcode has-text-grey"></i>
                                    </div>
                                </div>
                                <div class="media-content">
                                    <strong><?php echo htmlspecialchars($qr['title']); ?></strong>
                                    <br><small class="has-text-grey"><?php echo htmlspecialchars($qr['short_code']); ?></small>
                                </div>
                            </div>
                        </td>
                        <td>
                            <a href="<?php echo htmlspecialchars($qr['short_url']); ?>" target="_blank" class="has-text-link">
                                <?php echo htmlspecialchars($qr['short_url']); ?>
                            </a>
                        </td>
                        <td>
                            <?php if ($qr['target_url']): ?>
                            <span class="tag is-success is-light">Mapped</span>
                            <br><small><?php echo htmlspecialchars(substr($qr['target_url'], 0, 50)) . (strlen($qr['target_url']) > 50 ? '...' : ''); ?></small>
                            <?php else: ?>
                            <span class="tag is-warning is-light">Not Mapped</span>
                            <?php endif; ?>
                        </td>
                        <td>
                            <span class="tag <?php echo $qr['is_active'] ? 'is-success' : 'is-danger'; ?>">
                                <?php echo $qr['is_active'] ? 'Active' : 'Inactive'; ?>
                            </span>
                        </td>
                        <td><?php echo number_format($qr['scan_count']); ?></td>
                        <td><?php echo date('M j, Y', strtotime($qr['created_at'])); ?></td>
                        <td>
                            <div class="buttons are-small">
                                <a href="qr_codes.php?action=edit&id=<?php echo $qr['id']; ?>" 
                                   class="button is-small is-primary" title="Edit">
                                    <span class="icon"><i class="fas fa-edit"></i></span>
                                </a>
                                <?php if ($qr['target_url']): ?>
                                <a href="qr_codes.php?action=download&id=<?php echo $qr['id']; ?>" 
                                   class="button is-small is-success" title="Download QR">
                                    <span class="icon"><i class="fas fa-download"></i></span>
                                </a>
                                <?php else: ?>
                                <button class="button is-small" disabled title="Map URL first">
                                    <span class="icon"><i class="fas fa-download"></i></span>
                                </button>
                                <?php endif; ?>
                                <button class="button is-small is-danger confirm-delete" 
                                        data-url="qr_codes.php?action=delete&id=<?php echo $qr['id']; ?>" title="Delete">
                                    <span class="icon"><i class="fas fa-trash"></i></span>
                                </button>
                            </div>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
        <?php else: ?>
        <div class="has-text-centered py-6">
            <span class="icon is-large has-text-grey-light">
                <i class="fas fa-qrcode fa-3x"></i>
            </span>
            <p class="title is-5 has-text-grey mt-4">No QR codes yet</p>
            <p class="subtitle is-6 has-text-grey">Start by generating your first QR code</p>
            <a href="qr_codes.php?action=generate" class="button is-stormi-primary">
                <span class="icon"><i class="fas fa-plus"></i></span>
                <span>Generate QR Code</span>
            </a>
        </div>
        <?php endif; ?>
    </div>

    <?php elseif ($action === 'generate'): ?>
    <!-- Generate QR Code Form -->
    <div class="level mb-6">
        <div class="level-left">
            <div class="level-item">
                <div>
                    <h1 class="title is-3 mb-2 stormi-text-dark">Generate QR Code</h1>
                    <p class="subtitle is-5 mb-0 has-text-grey-dark">Create a new QR code with short URL</p>
                </div>
            </div>
        </div>
        <div class="level-right">
            <div class="level-item">
                <a href="qr_codes.php" class="button is-light">
                    <span class="icon"><i class="fas fa-arrow-left"></i></span>
                    <span>Back to QR Codes</span>
                </a>
            </div>
        </div>
    </div>

    <form method="POST">
        <div class="columns">
            <div class="column is-8">
                <div class="box">
                    <h2 class="title is-5 stormi-text-dark mb-4">
                        <span class="icon"><i class="fas fa-info-circle"></i></span>
                        QR Code Information
                    </h2>
                    
                    <div class="field">
                        <label class="label">Title <span class="has-text-danger">*</span></label>
                        <div class="control">
                            <input class="input" type="text" name="title" required
                                   placeholder="e.g., Artist Profile QR, Event QR">
                        </div>
                        <p class="help">Give your QR code a descriptive title</p>
                    </div>
                    
                    <div class="field">
                        <label class="label">Description</label>
                        <div class="control">
                            <textarea class="textarea" name="description" rows="3"
                                      placeholder="Optional description for internal reference"></textarea>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="column is-4">
                <div class="box">
                    <h2 class="title is-5 stormi-text-dark mb-4">
                        <span class="icon"><i class="fas fa-lightbulb"></i></span>
                        How it works
                    </h2>
                    
                    <div class="content">
                        <ol>
                            <li><strong>Generate:</strong> Create a QR code with unique short URL</li>
                            <li><strong>Map:</strong> Assign any target URL to the QR code</li>
                            <li><strong>Download:</strong> Get the QR code image for printing</li>
                            <li><strong>Track:</strong> Monitor scans and usage statistics</li>
                        </ol>
                    </div>
                    
                    <div class="notification is-info is-light">
                        <p class="has-text-weight-semibold">💡 Pro Tip</p>
                        <p>You can change the target URL anytime without regenerating the QR code!</p>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="field is-grouped is-grouped-right">
            <div class="control">
                <a href="qr_codes.php" class="button is-light">Cancel</a>
            </div>
            <div class="control">
                <button type="submit" class="button is-stormi-primary">
                    <span class="icon"><i class="fas fa-qrcode"></i></span>
                    <span>Generate QR Code</span>
                </button>
            </div>
        </div>
    </form>

    <?php elseif ($action === 'edit' && $qr_code): ?>
    <!-- Edit QR Code Form -->
    <div class="level mb-6">
        <div class="level-left">
            <div class="level-item">
                <div>
                    <h1 class="title is-3 mb-2 stormi-text-dark">Edit QR Code</h1>
                    <p class="subtitle is-5 mb-0 has-text-grey-dark">Configure URL mapping and settings</p>
                </div>
            </div>
        </div>
        <div class="level-right">
            <div class="level-item">
                <a href="qr_codes.php" class="button is-light">
                    <span class="icon"><i class="fas fa-arrow-left"></i></span>
                    <span>Back to QR Codes</span>
                </a>
            </div>
        </div>
    </div>

    <form method="POST">
        <div class="columns">
            <div class="column is-8">
                <div class="box">
                    <h2 class="title is-5 stormi-text-dark mb-4">
                        <span class="icon"><i class="fas fa-link"></i></span>
                        URL Mapping
                    </h2>
                    
                    <div class="field">
                        <label class="label">Short URL</label>
                        <div class="control">
                            <input class="input" type="text" value="<?php echo htmlspecialchars($qr_code['short_url']); ?>" readonly>
                        </div>
                        <p class="help">This is your QR code's permanent short URL</p>
                    </div>
                    
                    <div class="field">
                        <label class="label">Target URL</label>
                        <div class="control">
                            <input class="input" type="url" name="target_url" 
                                   value="<?php echo htmlspecialchars($qr_code['target_url'] ?? ''); ?>"
                                   placeholder="https://example.com/page">
                        </div>
                        <p class="help">Where should this QR code redirect users?</p>
                    </div>
                    
                    <div class="field">
                        <label class="label">Quick Artist Mapping</label>
                        <div class="control">
                            <div class="select is-fullwidth">
                                <select id="artist-select">
                                    <option value="">Select an artist to auto-fill URL...</option>
                                    <?php foreach ($artists as $artist): ?>
                                    <option value="<?php echo SITE_URL; ?>/?page=artist&slug=<?php echo htmlspecialchars($artist['slug']); ?>">
                                        <?php echo htmlspecialchars($artist['translated_name'] ?: $artist['name']); ?>
                                    </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                        <p class="help">Quick way to map to an artist profile page</p>
                    </div>
                </div>
                
                <div class="box">
                    <h2 class="title is-5 stormi-text-dark mb-4">
                        <span class="icon"><i class="fas fa-info-circle"></i></span>
                        QR Code Information
                    </h2>
                    
                    <div class="field">
                        <label class="label">Title</label>
                        <div class="control">
                            <input class="input" type="text" name="title" 
                                   value="<?php echo htmlspecialchars($qr_code['title']); ?>">
                        </div>
                    </div>
                    
                    <div class="field">
                        <label class="label">Description</label>
                        <div class="control">
                            <textarea class="textarea" name="description" rows="3"><?php echo htmlspecialchars($qr_code['description'] ?? ''); ?></textarea>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="column is-4">
                <div class="box">
                    <h2 class="title is-5 stormi-text-dark mb-4">
                        <span class="icon"><i class="fas fa-qrcode"></i></span>
                        QR Code Preview
                    </h2>
                    
                    <div class="has-text-centered mb-4">
                        <div id="qr-preview" style="width: 200px; height: 200px; margin: 0 auto; background: #f5f5f5; border-radius: 8px; display: flex; align-items: center; justify-content: center;">
                            <i class="fas fa-qrcode fa-3x has-text-grey"></i>
                        </div>
                    </div>
                    
                    <?php if ($qr_code['target_url']): ?>
                    <a href="qr_codes.php?action=download&id=<?php echo $qr_code['id']; ?>" 
                       class="button is-success is-fullwidth">
                        <span class="icon"><i class="fas fa-download"></i></span>
                        <span>Download QR Code</span>
                    </a>
                    <?php else: ?>
                    <button class="button is-fullwidth" disabled>
                        <span class="icon"><i class="fas fa-download"></i></span>
                        <span>Map URL to Download</span>
                    </button>
                    <?php endif; ?>
                </div>
                
                <div class="box">
                    <h2 class="title is-5 stormi-text-dark mb-4">
                        <span class="icon"><i class="fas fa-chart-bar"></i></span>
                        Statistics
                    </h2>
                    
                    <div class="level">
                        <div class="level-item has-text-centered">
                            <div>
                                <p class="heading">Total Scans</p>
                                <p class="title is-4"><?php echo number_format($qr_code['scan_count']); ?></p>
                            </div>
                        </div>
                        <div class="level-item has-text-centered">
                            <div>
                                <p class="heading">Downloads</p>
                                <p class="title is-4"><?php echo number_format($qr_code['download_count']); ?></p>
                            </div>
                        </div>
                    </div>
                    
                    <p class="help has-text-centered">
                        Created <?php echo date('M j, Y', strtotime($qr_code['created_at'])); ?>
                    </p>
                </div>
            </div>
        </div>
        
        <div class="field is-grouped is-grouped-right">
            <div class="control">
                <a href="qr_codes.php" class="button is-light">Cancel</a>
            </div>
            <div class="control">
                <button type="submit" class="button is-stormi-primary">
                    <span class="icon"><i class="fas fa-save"></i></span>
                    <span>Update QR Code</span>
                </button>
            </div>
        </div>
    </form>
    
    <?php endif; ?>
</div>

<script>
// Auto-fill target URL when artist is selected
document.addEventListener('DOMContentLoaded', function() {
    const artistSelect = document.getElementById('artist-select');
    const targetUrlInput = document.querySelector('input[name="target_url"]');
    
    if (artistSelect && targetUrlInput) {
        artistSelect.addEventListener('change', function() {
            if (this.value) {
                targetUrlInput.value = this.value;
            }
        });
    }
    
    // Notification close buttons
    document.querySelectorAll('.notification .delete').forEach(button => {
        button.addEventListener('click', function() {
            this.parentElement.remove();
        });
    });
});
</script>

<?php include 'includes/admin_footer.php'; ?>
