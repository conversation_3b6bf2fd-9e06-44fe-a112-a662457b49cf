/**
 * Advanced Analytics Tracking Library
 * Handles user journey, gallery interactions, social clicks, heatmaps, and conversion funnels
 */

class AdvancedAnalytics {
    constructor() {
        this.sessionStartTime = Date.now();
        this.pageStartTime = Date.now();
        this.scrollDepth = 0;
        this.maxScrollDepth = 0;
        this.interactions = 0;
        this.heatmapData = [];
        this.isTracking = true;
        
        this.init();
    }
    
    init() {
        // Track page entry
        this.trackPageEntry();
        
        // Set up event listeners
        this.setupScrollTracking();
        this.setupClickTracking();
        this.setupHoverTracking();
        this.setupGalleryTracking();
        this.setupSocialTracking();
        this.setupPerformanceTracking();
        
        // Track page exit
        this.setupExitTracking();
        
        // Send heatmap data periodically
        setInterval(() => this.sendHeatmapData(), 10000); // Every 10 seconds
    }
    
    trackPageEntry() {
        const pageData = {
            url: window.location.href,
            title: document.title,
            artist_id: this.getArtistId(),
            referrer: document.referrer,
            timestamp: new Date().toISOString()
        };
        
        this.sendData('track_journey', pageData);
        this.trackConversionStep('landing');
    }
    
    setupScrollTracking() {
        let ticking = false;
        
        const updateScrollDepth = () => {
            const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
            const docHeight = document.documentElement.scrollHeight - window.innerHeight;
            const scrollPercent = Math.round((scrollTop / docHeight) * 100);
            
            this.scrollDepth = scrollPercent;
            this.maxScrollDepth = Math.max(this.maxScrollDepth, scrollPercent);
            
            ticking = false;
        };
        
        window.addEventListener('scroll', () => {
            if (!ticking) {
                requestAnimationFrame(updateScrollDepth);
                ticking = true;
            }
        });
    }
    
    setupClickTracking() {
        document.addEventListener('click', (e) => {
            this.interactions++;
            
            const rect = e.target.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;
            
            this.addHeatmapPoint('click', e.clientX, e.clientY, e.target);
            
            // Track specific element clicks
            if (e.target.closest('.gallery-item, .masonry-item')) {
                this.trackGalleryInteraction('click', e.target, x, y);
            }
        });
    }
    
    setupHoverTracking() {
        let hoverTimeout;
        
        document.addEventListener('mouseover', (e) => {
            clearTimeout(hoverTimeout);
            hoverTimeout = setTimeout(() => {
                this.addHeatmapPoint('hover', e.clientX, e.clientY, e.target);
            }, 1000); // Track hovers longer than 1 second
        });
        
        document.addEventListener('mouseout', () => {
            clearTimeout(hoverTimeout);
        });
    }
    
    setupGalleryTracking() {
        // Track gallery image views
        const observeGalleryImages = () => {
            const images = document.querySelectorAll('.masonry-item img, .gallery-item img');
            
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const imageId = entry.target.dataset.imageId;
                        this.trackGalleryInteraction('view', entry.target, null, null, {
                            imageId: imageId,
                            visibilityRatio: entry.intersectionRatio
                        });
                    }
                });
            }, { threshold: 0.5 });
            
            images.forEach(img => observer.observe(img));
        };
        
        // Wait for DOM to be ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', observeGalleryImages);
        } else {
            observeGalleryImages();
        }
        
        // Track modal interactions
        document.addEventListener('click', (e) => {
            if (e.target.closest('.masonry-item, .gallery-item')) {
                this.trackGalleryInteraction('modal_open', e.target);
                this.trackConversionStep('gallery_view');
            }
        });
    }
    
    setupSocialTracking() {
        document.addEventListener('click', (e) => {
            const socialLink = e.target.closest('.social-link, [href*="facebook"], [href*="instagram"], [href*="twitter"], [href*="linkedin"], [href*="youtube"]');
            
            if (socialLink) {
                const platform = this.detectSocialPlatform(socialLink.href);
                const position = this.getElementPosition(socialLink);
                
                this.trackSocialClick(platform, socialLink.href, position);
                this.trackConversionStep('social_click', {
                    platform: platform,
                    url: socialLink.href
                });
            }
        });
    }
    
    setupPerformanceTracking() {
        window.addEventListener('load', () => {
            setTimeout(() => {
                const perfData = performance.getEntriesByType('navigation')[0];
                if (perfData) {
                    this.sendData('track_performance', {
                        load_time: perfData.loadEventEnd - perfData.loadEventStart,
                        time_to_first_byte: perfData.responseStart - perfData.requestStart,
                        dom_content_loaded: perfData.domContentLoadedEventEnd - perfData.domContentLoadedEventStart,
                        artist_id: this.getArtistId()
                    });
                }
            }, 1000);
        });
    }
    
    setupExitTracking() {
        const trackExit = () => {
            const timeSpent = Math.round((Date.now() - this.pageStartTime) / 1000);
            
            this.sendData('track_exit', {
                time_spent: timeSpent,
                scroll_depth: this.maxScrollDepth,
                interactions: this.interactions,
                artist_id: this.getArtistId()
            }, true); // Synchronous for page unload
        };
        
        window.addEventListener('beforeunload', trackExit);
        window.addEventListener('pagehide', trackExit);
        
        // Also track when user becomes inactive
        let inactiveTimeout;
        const resetInactiveTimer = () => {
            clearTimeout(inactiveTimeout);
            inactiveTimeout = setTimeout(trackExit, 300000); // 5 minutes of inactivity
        };
        
        ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart'].forEach(event => {
            document.addEventListener(event, resetInactiveTimer, true);
        });
    }
    
    addHeatmapPoint(type, x, y, element) {
        this.heatmapData.push({
            type: type,
            x: x,
            y: y,
            element: this.getElementSelector(element),
            viewport_width: window.innerWidth,
            viewport_height: window.innerHeight,
            timestamp: Date.now()
        });
    }
    
    sendHeatmapData() {
        if (this.heatmapData.length > 0) {
            this.sendData('track_heatmap', {
                heatmap_data: this.heatmapData,
                artist_id: this.getArtistId()
            });
            this.heatmapData = []; // Clear sent data
        }
    }
    
    trackGalleryInteraction(type, element, x = null, y = null, data = null) {
        this.sendData('track_gallery', {
            interaction_type: type,
            gallery_image_id: element?.dataset?.imageId || null,
            x_coordinate: x,
            y_coordinate: y,
            interaction_data: data,
            artist_id: this.getArtistId()
        });
    }
    
    trackSocialClick(platform, url, position) {
        this.sendData('track_social', {
            platform: platform,
            url: url,
            position: position,
            artist_id: this.getArtistId()
        });
    }
    
    trackConversionStep(step, data = null) {
        this.sendData('track_conversion', {
            step: step,
            data: data,
            artist_id: this.getArtistId()
        });
    }
    
    // Utility methods
    getArtistId() {
        const urlParams = new URLSearchParams(window.location.search);
        return urlParams.get('artist_id') || document.body.dataset.artistId || null;
    }
    
    detectSocialPlatform(url) {
        if (url.includes('facebook.com')) return 'facebook';
        if (url.includes('instagram.com')) return 'instagram';
        if (url.includes('twitter.com') || url.includes('x.com')) return 'twitter';
        if (url.includes('linkedin.com')) return 'linkedin';
        if (url.includes('youtube.com')) return 'youtube';
        if (url.includes('tiktok.com')) return 'tiktok';
        return 'other';
    }
    
    getElementPosition(element) {
        const rect = element.getBoundingClientRect();
        return {
            top: rect.top,
            left: rect.left,
            width: rect.width,
            height: rect.height
        };
    }
    
    getElementSelector(element) {
        if (!element) return null;
        
        if (element.id) return `#${element.id}`;
        if (element.className) return `.${element.className.split(' ')[0]}`;
        return element.tagName.toLowerCase();
    }
    
    sendData(endpoint, data, sync = false) {
        if (!this.isTracking) return;
        
        const url = `includes/analytics_endpoints.php?action=${endpoint}`;
        
        if (sync) {
            // Synchronous request for page unload
            const xhr = new XMLHttpRequest();
            xhr.open('POST', url, false);
            xhr.setRequestHeader('Content-Type', 'application/json');
            xhr.send(JSON.stringify(data));
        } else {
            // Asynchronous request
            fetch(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data)
            }).catch(error => {
                console.error('Analytics tracking error:', error);
            });
        }
    }
}

// Initialize analytics when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    window.advancedAnalytics = new AdvancedAnalytics();
});

// Export for manual usage
window.AdvancedAnalytics = AdvancedAnalytics;
