<?php
/**
 * Demographics Collection Modal
 * Collects user demographics with consent for analytics
 */
?>

<!-- Demographics Collection Modal -->
<div id="demographicsModal" class="modal">
    <div class="modal-background"></div>
    <div class="modal-card">
        <header class="modal-card-head">
            <p class="modal-card-title">
                <span class="icon"><i class="fas fa-chart-bar"></i></span>
                Help Us Improve Your Experience
            </p>
        </header>
        <section class="modal-card-body">
            <div class="content">
                <p class="mb-4">
                    <strong>Optional:</strong> Help us understand our audience better by sharing some basic information. 
                    This data is completely anonymous and helps us create better content for our visitors.
                </p>
                
                <div class="field">
                    <label class="label">Age Range</label>
                    <div class="control">
                        <div class="select is-fullwidth">
                            <select id="ageRange">
                                <option value="">Prefer not to say</option>
                                <option value="under-18">Under 18</option>
                                <option value="18-24">18-24</option>
                                <option value="25-34">25-34</option>
                                <option value="35-44">35-44</option>
                                <option value="45-54">45-54</option>
                                <option value="55-64">55-64</option>
                                <option value="65-plus">65+</option>
                            </select>
                        </div>
                    </div>
                </div>
                
                <div class="field">
                    <label class="label">Gender</label>
                    <div class="control">
                        <div class="select is-fullwidth">
                            <select id="gender">
                                <option value="">Prefer not to say</option>
                                <option value="male">Male</option>
                                <option value="female">Female</option>
                                <option value="non-binary">Non-binary</option>
                                <option value="other">Other</option>
                            </select>
                        </div>
                    </div>
                </div>
                
                <div class="field">
                    <div class="control">
                        <label class="checkbox">
                            <input type="checkbox" id="consentCheckbox" checked>
                            I consent to sharing this anonymous demographic data for analytics purposes
                        </label>
                    </div>
                </div>
                
                <div class="notification is-info is-light">
                    <p class="is-size-7">
                        <strong>Privacy Notice:</strong> This information is collected anonymously and will only be used 
                        for statistical analysis. We do not store any personally identifiable information.
                    </p>
                </div>
            </div>
        </section>
        <footer class="modal-card-foot">
            <button class="button is-primary" onclick="submitDemographics()">
                <span class="icon"><i class="fas fa-check"></i></span>
                <span>Submit</span>
            </button>
            <button class="button" onclick="skipDemographics()">
                <span>Skip</span>
            </button>
        </footer>
    </div>
</div>

<script>
// Demographics collection functionality
function showDemographicsModal() {
    // Check if user has already provided demographics in this session
    if (localStorage.getItem('demographics_provided') === 'true') {
        return;
    }
    
    // Show modal after a short delay
    setTimeout(() => {
        document.getElementById('demographicsModal').classList.add('is-active');
    }, 3000); // Show after 3 seconds
}

function submitDemographics() {
    const ageRange = document.getElementById('ageRange').value;
    const gender = document.getElementById('gender').value;
    const consent = document.getElementById('consentCheckbox').checked;
    
    if (consent && (ageRange || gender)) {
        // Send demographics data to server
        fetch('includes/update_demographics.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                age_range: ageRange,
                gender: gender,
                consent: consent
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                console.log('Demographics updated successfully');
            }
        })
        .catch(error => {
            console.error('Error updating demographics:', error);
        });
    }
    
    // Mark as provided and close modal
    localStorage.setItem('demographics_provided', 'true');
    closeDemographicsModal();
}

function skipDemographics() {
    // Mark as provided (skipped) and close modal
    localStorage.setItem('demographics_provided', 'true');
    closeDemographicsModal();
}

function closeDemographicsModal() {
    document.getElementById('demographicsModal').classList.remove('is-active');
}

// Auto-show demographics modal on page load
document.addEventListener('DOMContentLoaded', function() {
    // Only show on artist pages and not too frequently
    if (window.location.pathname.includes('artist') || window.location.search.includes('page=artist')) {
        showDemographicsModal();
    }
});
</script>

<style>
.modal-card {
    max-width: 500px;
    margin: 0 auto;
}

.modal-card-head {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.modal-card-title {
    color: white;
}

.modal-card-foot {
    justify-content: flex-end;
}

@media (max-width: 768px) {
    .modal-card {
        margin: 1rem;
        max-width: calc(100% - 2rem);
    }
}
</style>
