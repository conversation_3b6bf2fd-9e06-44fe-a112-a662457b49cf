<?php
/**
 * Admin Dashboard - Main Page
 */

require_once '../config/config.php';
require_once '../config/database.php';
require_once '../classes/Auth.php';

session_start();

$auth = new Auth();
$auth->requireLogin();

$current_user = $auth->getCurrentUser();

// Get dashboard statistics
$stats = [];
try {
    $db = Database::getInstance();
    
    // Total artists
    $stmt = $db->query("SELECT COUNT(*) as count FROM artists WHERE is_active = 1");
    $stats['total_artists'] = $stmt->fetch()['count'] ?? 0;
    
    // Local artists (Sri Lankan artists)
    $stmt = $db->query("SELECT COUNT(*) as count FROM artists WHERE is_active = 1 AND is_featured = 1");
    $stats['local_artists'] = $stmt->fetch()['count'] ?? 0;
    
    // Total gallery images
    $stmt = $db->query("SELECT COUNT(*) as count FROM artist_galleries WHERE is_active = 1");
    $stats['total_images'] = $stmt->fetch()['count'] ?? 0;
    
    // Total QR codes
    $stmt = $db->query("SELECT COUNT(*) as count FROM qr_codes WHERE is_active = 1");
    $stats['total_qr_codes'] = $stmt->fetch()['count'] ?? 0;
    
    // Total visits this month
    $stmt = $db->query("SELECT COUNT(*) as count FROM analytics_visits WHERE visited_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)");
    $stats['monthly_visits'] = $stmt->fetch()['count'] ?? 0;
    
    // Recent artists with translations (simplified query)
    $stmt = $db->query("SELECT a.id, a.name, a.slug, a.country, a.main_medium, a.created_at, a.is_active, a.is_featured, a.email,
                              COALESCE(at.name, a.name) as display_name
                       FROM artists a
                       LEFT JOIN artist_translations at ON a.id = at.artist_id AND at.language_code = 'en'
                       WHERE a.is_active = 1
                       ORDER BY a.created_at DESC LIMIT 10");
    $recent_artists = $stmt->fetchAll();

    // Get QR mappings separately for each artist (with error handling)
    foreach ($recent_artists as &$artist) {
        try {
            // First try to find QR codes mapped to this artist
            $qr_stmt = $db->query("SELECT qr.id, qr.short_code, qr.target_url
                                  FROM qr_artist_mappings qam
                                  JOIN qr_codes qr ON qam.qr_code_id = qr.id
                                  WHERE qam.artist_id = ? AND qam.is_primary = 1 AND qr.is_active = 1
                                  LIMIT 1", [$artist['id']]);
            $qr_data = $qr_stmt->fetch();

            // If no specific mapping found, check if there's a QR code with target URL pointing to this artist
            if (!$qr_data) {
                $artist_url = SITE_URL . '/?page=artist&slug=' . $artist['slug'];
                $qr_stmt = $db->query("SELECT id, short_code, target_url
                                      FROM qr_codes
                                      WHERE target_url = ? AND is_active = 1
                                      LIMIT 1", [$artist_url]);
                $qr_data = $qr_stmt->fetch();
            }

            $artist['qr_id'] = $qr_data ? $qr_data['id'] : null;
            $artist['short_code'] = $qr_data ? $qr_data['short_code'] : null;
            $artist['target_url'] = $qr_data ? $qr_data['target_url'] : null;
        } catch (Exception $e) {
            // If QR query fails, just set null values
            $artist['qr_id'] = null;
            $artist['short_code'] = null;
            $artist['target_url'] = null;
        }
    }
    
    // Recent analytics
    $stmt = $db->query("
        SELECT av.*, a.name as artist_name 
        FROM analytics_visits av 
        LEFT JOIN artists a ON av.artist_id = a.id 
        ORDER BY av.visited_at DESC 
        LIMIT 10
    ");
    $recent_visits = $stmt->fetchAll();
    
} catch (Exception $e) {
    error_log("Dashboard stats error: " . $e->getMessage());
    $stats = [
        'total_artists' => 0,
        'local_artists' => 0,
        'total_images' => 0,
        'total_qr_codes' => 0,
        'monthly_visits' => 0
    ];
    $recent_artists = [];
    $recent_visits = [];
}

// Page configuration
$page_title = 'Dashboard';
$page_description = 'Admin dashboard for Artist Portfolio Management System';
$body_class = 'admin-dashboard';

include_once 'includes/admin_header.php';
?>

<div class="container is-fluid">
    <?php if (isset($_GET['success'])): ?>
    <div class="notification is-success is-light">
        <button class="delete"></button>
        <?php echo htmlspecialchars($_GET['success']); ?>
    </div>
    <?php endif; ?>

    <!-- Dashboard Header -->
    <div class="level mb-6">
        <div class="level-left">
            <div class="level-item">
                <div>
                    <h1 class="title is-3 mb-2 stormi-text-dark">Dashboard</h1>
                    <p class="subtitle is-5 mb-0 has-text-grey-dark">Welcome back, System Administrator</p>
                </div>
            </div>
        </div>
        <div class="level-right">
            <div class="level-item">
                <div class="buttons">
                    <a href="artists.php?action=create" class="button is-stormi-primary is-small">
                        <span class="icon is-small"><i class="fas fa-plus"></i></span>
                        <span>Add Artist</span>
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Compact Statistics Cards -->
    <div class="columns is-multiline mb-4">
        <div class="column is-3">
            <div class="box stormi-bg-lightest p-5">
                <div class="level is-mobile mb-0">
                    <div class="level-left">
                        <div class="columns is-mobile is-gapless">
                            <div class="column is-narrow">
                                <p class="title is-4 mb-0 stormi-text-dark mr-3"><?php echo number_format($stats['total_artists']); ?></p>
                            </div>
                            <div class="column">
                                <p class="subtitle is-6 mb-0 stormi-text-medium">Total Artists</p>
                            </div>
                        </div>
                    </div>
                    <div class="level-right">
                        <span class="icon stormi-text-dark">
                            <i class="fas fa-users fa-2x"></i>
                        </span>
                    </div>
                </div>
            </div>
        </div>

        <div class="column is-3">
            <div class="box stormi-bg-light p-5">
                <div class="level is-mobile mb-0">
                    <div class="level-left">
                        <div class="columns is-mobile is-gapless">
                            <div class="column is-narrow">
                                <p class="title is-4 mb-0 has-text-white mr-3"><?php echo number_format($stats['local_artists']); ?></p>
                            </div>
                            <div class="column">
                                <p class="subtitle is-6 mb-0 has-text-white">Local Artists</p>
                            </div>
                        </div>
                    </div>
                    <div class="level-right">
                        <span class="icon has-text-white">
                            <i class="fas fa-map-marker-alt fa-2x"></i>
                        </span>
                    </div>
                </div>
            </div>
        </div>

        <div class="column is-3">
            <div class="box stormi-bg-medium p-5">
                <div class="level is-mobile mb-0">
                    <div class="level-left">
                        <div class="columns is-mobile is-gapless">
                            <div class="column is-narrow">
                                <p class="title is-4 mb-0 has-text-white mr-3"><?php echo number_format($stats['total_images']); ?></p>
                            </div>
                            <div class="column">
                                <p class="subtitle is-6 mb-0 has-text-white">Gallery Images</p>
                            </div>
                        </div>
                    </div>
                    <div class="level-right">
                        <span class="icon has-text-white">
                            <i class="fas fa-images fa-2x"></i>
                        </span>
                    </div>
                </div>
            </div>
        </div>

        <div class="column is-3">
            <div class="box stormi-bg-dark p-5">
                <div class="level is-mobile mb-0">
                    <div class="level-left">
                        <div class="columns is-mobile is-gapless">
                            <div class="column is-narrow">
                                <p class="title is-4 mb-0 has-text-white mr-3"><?php echo number_format($stats['monthly_visits']); ?></p>
                            </div>
                            <div class="column">
                                <p class="subtitle is-6 mb-0 has-text-white">Monthly Visits</p>
                            </div>
                        </div>
                    </div>
                    <div class="level-right">
                        <span class="icon has-text-white">
                            <i class="fas fa-chart-line fa-2x"></i>
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Main Content -->
    <div class="columns">
        <!-- Recent Artists -->
        <div class="column is-8">
            <div class="box">
                <div class="level is-mobile mb-3">
                    <div class="level-left">
                        <div class="level-item">
                            <h3 class="title is-6 mb-0">
                                <span class="icon is-small"><i class="fas fa-users"></i></span>
                                Recent Artists
                            </h3>
                        </div>
                    </div>
                    <div class="level-right">
                        <div class="level-item">
                            <a href="artists.php?action=create" class="button is-small is-stormi-primary">
                                <span class="icon is-small"><i class="fas fa-plus"></i></span>
                                <span>Add Artist</span>
                            </a>
                        </div>
                    </div>
                </div>

                <?php if ($recent_artists): ?>
                <div class="table-container">
                    <table class="table is-fullwidth is-hoverable is-narrow">
                        <thead>
                            <tr>
                                <th>Artist</th>
                                <th>Country</th>
                                <th>Status</th>
                                <th>Created</th>
                                <th width="140">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($recent_artists as $artist): ?>
                            <tr>
                                <td>
                                    <div>
                                        <strong><?php echo htmlspecialchars($artist['display_name'] ?: $artist['name']); ?></strong>
                                        <?php if ($artist['is_featured']): ?>
                                        <span class="tag is-small is-warning ml-2">Local</span>
                                        <?php endif; ?>
                                    </div>
                                </td>
                                <td><?php echo htmlspecialchars($artist['country'] ?? 'N/A'); ?></td>
                                <td>
                                    <span class="tag is-small <?php echo $artist['is_active'] ? 'is-success' : 'is-danger'; ?>">
                                        <?php echo $artist['is_active'] ? 'Active' : 'Inactive'; ?>
                                    </span>
                                </td>
                                <td><?php echo date('M j', strtotime($artist['created_at'])); ?></td>
                                <td>
                                    <div class="buttons are-small">
                                        <a href="artists.php?action=edit&id=<?php echo $artist['id']; ?>" class="button is-small is-primary" title="Edit">
                                            <span class="icon is-small"><i class="fas fa-edit"></i></span>
                                        </a>
                                        <a href="<?php echo SITE_URL; ?>/?page=artist&slug=<?php echo htmlspecialchars($artist['slug']); ?>" class="button is-small is-info" target="_blank" title="View Profile">
                                            <span class="icon is-small"><i class="fas fa-eye"></i></span>
                                        </a>
                                        <?php if ($artist['qr_id'] && $artist['target_url']): ?>
                                        <a href="qr_codes.php?action=download&id=<?php echo $artist['qr_id']; ?>" class="button is-small is-success" title="Download QR Code">
                                            <span class="icon is-small"><i class="fas fa-qrcode"></i></span>
                                        </a>
                                        <?php else: ?>
                                        <a href="qr_codes.php?action=generate&artist_id=<?php echo $artist['id']; ?>" class="button is-small is-warning" title="Create QR Code for Artist">
                                            <span class="icon is-small"><i class="fas fa-qrcode"></i></span>
                                        </a>
                                        <?php endif; ?>
                                        <button class="button is-small is-danger confirm-delete"
                                                data-url="artists.php?action=delete&id=<?php echo $artist['id']; ?>" title="Delete">
                                            <span class="icon is-small"><i class="fas fa-trash"></i></span>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                <?php else: ?>
                <div class="has-text-centered py-4">
                    <span class="icon is-large has-text-grey-light">
                        <i class="fas fa-users fa-2x"></i>
                    </span>
                    <p class="title is-6 has-text-grey mt-2">No artists yet</p>
                    <p class="subtitle is-7 has-text-grey">Start by adding your first artist</p>
                    <a href="artists.php?action=create" class="button is-stormi-primary is-small">
                        <span class="icon is-small"><i class="fas fa-plus"></i></span>
                        <span>Add First Artist</span>
                    </a>
                </div>
                <?php endif; ?>
            </div>
        </div>
        
        <!-- Sidebar -->
        <div class="column is-4">
            <!-- Recent Activity -->
            <div class="box">
                <h3 class="title is-6 mb-3">
                    <span class="icon is-small"><i class="fas fa-clock"></i></span>
                    Recent Activity
                </h3>

                <?php if ($recent_visits): ?>
                <div class="content">
                    <?php foreach (array_slice($recent_visits, 0, 4) as $visit): ?>
                    <div class="media is-small">
                        <div class="media-left">
                            <span class="icon is-small has-text-info">
                                <i class="fas fa-<?php echo $visit['visit_type'] === 'qr_scan' ? 'qrcode' : ($visit['visit_type'] === 'short_url_click' ? 'link' : 'eye'); ?>"></i>
                            </span>
                        </div>
                        <div class="media-content">
                            <p class="is-size-7">
                                <strong><?php echo ucfirst(str_replace('_', ' ', $visit['visit_type'])); ?></strong>
                                <?php if ($visit['artist_name']): ?>
                                for <em><?php echo htmlspecialchars($visit['artist_name']); ?></em>
                                <?php endif; ?>
                                <br>
                                <small class="has-text-grey"><?php echo date('M j, g:i A', strtotime($visit['visited_at'])); ?></small>
                            </p>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
                <?php else: ?>
                <div class="has-text-centered py-3">
                    <span class="icon has-text-grey-light">
                        <i class="fas fa-clock"></i>
                    </span>
                    <p class="is-size-7 has-text-grey mt-1">No recent activity</p>
                </div>
                <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- DEBUG: Add direct delete handler -->
<script>
console.log('=== DASHBOARD DEBUG SCRIPT ===');

document.addEventListener('DOMContentLoaded', function() {
    console.log('Dashboard DOM loaded');

    // Find all delete buttons
    const deleteButtons = document.querySelectorAll('.confirm-delete');
    console.log('Found delete buttons:', deleteButtons.length);

    deleteButtons.forEach((btn, index) => {
        console.log('Delete button ' + index + ':', btn);
        console.log('Data URL:', btn.getAttribute('data-url'));
    });

    // Add direct click handler
    document.addEventListener('click', function(e) {
        console.log('Click detected on:', e.target);
        console.log('Target classes:', e.target.className);

        if (e.target.classList.contains('confirm-delete') || e.target.closest('.confirm-delete')) {
            console.log('DELETE BUTTON CLICKED!');
            e.preventDefault();

            const deleteButton = e.target.closest('.confirm-delete') || e.target;
            const deleteUrl = deleteButton.getAttribute('data-url');

            console.log('Delete URL:', deleteUrl);

            if (deleteUrl && confirm('DEBUG: Delete this item? URL: ' + deleteUrl)) {
                console.log('User confirmed, redirecting to:', deleteUrl);
                window.location.href = deleteUrl;
            }
            return false;
        }
    });

    console.log('Dashboard delete handler loaded');
});

console.log('=== DASHBOARD DEBUG SCRIPT LOADED ===');
</script>

<?php include 'includes/admin_footer.php'; ?>
