<?php
/**
 * Database Connection Test
 * Upload this to your live server to test database connectivity
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Database Connection Test</h1>";

// Include config
if (file_exists('config/config.php')) {
    require_once 'config/config.php';
    echo "✅ Config file found<br>";
} else {
    echo "❌ Config file not found<br>";
    exit;
}

echo "<h2>Configuration Check:</h2>";
echo "DB_HOST: " . DB_HOST . "<br>";
echo "DB_NAME: " . DB_NAME . "<br>";
echo "DB_USER: " . DB_USER . "<br>";
echo "DB_PASS: " . (DB_PASS ? '[SET]' : '[EMPTY]') . "<br>";
echo "SITE_URL: " . SITE_URL . "<br>";

echo "<h2>Database Connection Test:</h2>";

try {
    // Test basic PDO connection
    $dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4";
    $pdo = new PDO($dsn, DB_USER, DB_PASS, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::ATTR_EMULATE_PREPARES => false
    ]);
    
    echo "✅ Database connection successful<br>";
    
    // Test if tables exist
    echo "<h3>Table Check:</h3>";
    $tables = ['users', 'artists', 'analytics_visits', 'analytics_user_journeys'];
    
    foreach ($tables as $table) {
        try {
            $stmt = $pdo->query("SELECT COUNT(*) FROM {$table}");
            $count = $stmt->fetchColumn();
            echo "✅ Table '{$table}': {$count} records<br>";
        } catch (Exception $e) {
            echo "❌ Table '{$table}': " . $e->getMessage() . "<br>";
        }
    }
    
    // Test admin user
    echo "<h3>Admin User Check:</h3>";
    try {
        $stmt = $pdo->query("SELECT id, username, email FROM users WHERE role = 'admin'");
        $admins = $stmt->fetchAll();
        
        if ($admins) {
            echo "✅ Admin users found:<br>";
            foreach ($admins as $admin) {
                echo "- ID: {$admin['id']}, Username: {$admin['username']}, Email: {$admin['email']}<br>";
            }
        } else {
            echo "❌ No admin users found<br>";
        }
    } catch (Exception $e) {
        echo "❌ Admin user check failed: " . $e->getMessage() . "<br>";
    }
    
    // Test Database class
    echo "<h3>Database Class Test:</h3>";
    if (file_exists('config/database.php')) {
        require_once 'config/database.php';
        try {
            $db = Database::getInstance();
            $result = $db->query("SELECT 1 as test")->fetch();
            echo "✅ Database class working: " . $result['test'] . "<br>";
        } catch (Exception $e) {
            echo "❌ Database class error: " . $e->getMessage() . "<br>";
        }
    } else {
        echo "❌ Database class file not found<br>";
    }
    
} catch (Exception $e) {
    echo "❌ Database connection failed: " . $e->getMessage() . "<br>";
    echo "Error details: " . $e->getTraceAsString() . "<br>";
}

echo "<h2>File Permissions Check:</h2>";
$directories = ['uploads', 'qr_codes', 'config'];
foreach ($directories as $dir) {
    if (is_dir($dir)) {
        $perms = substr(sprintf('%o', fileperms($dir)), -4);
        $writable = is_writable($dir) ? '✅ Writable' : '❌ Not writable';
        echo "Directory '{$dir}': {$perms} - {$writable}<br>";
    } else {
        echo "❌ Directory '{$dir}' not found<br>";
    }
}

echo "<h2>PHP Information:</h2>";
echo "PHP Version: " . phpversion() . "<br>";
echo "Server: " . $_SERVER['SERVER_SOFTWARE'] . "<br>";
echo "Document Root: " . $_SERVER['DOCUMENT_ROOT'] . "<br>";
echo "Current Directory: " . __DIR__ . "<br>";

echo "<h2>Session Test:</h2>";
session_start();
$_SESSION['test'] = 'working';
echo "Session ID: " . session_id() . "<br>";
echo "Session Test: " . $_SESSION['test'] . "<br>";

echo "<p><strong>Next Steps:</strong></p>";
echo "<ul>";
echo "<li>If database connection failed, update config/config.php with correct credentials</li>";
echo "<li>If tables are missing, re-import your database</li>";
echo "<li>If admin user is missing, create one manually or re-import database</li>";
echo "<li>If file permissions are wrong, set uploads/ and qr_codes/ to 755 or 777</li>";
echo "<li>Once everything is green, try logging in again</li>";
echo "</ul>";

echo "<p><a href='admin/login.php'>Try Admin Login</a></p>";
?>
