# Basic URL Rewriting for Artist Portfolio
RewriteEngine On

# Debug - Enable rewrite logging (remove in production)
# RewriteLog /tmp/rewrite.log
# RewriteLogLevel 3

# Admin panel access
RewriteRule ^admin/?$ admin/index.php [L]
RewriteRule ^admin/(.*)$ admin/$1 [L]

# Artist URLs - Clean URLs
RewriteRule ^artist/([a-zA-Z0-9\-]+)/?$ index.php?page=artist&slug=$1 [L,QSA]

# QR Code short URLs
RewriteRule ^qr/([A-Z0-9]+)/?$ includes/qr_track.php?code=$1 [L,QSA]
