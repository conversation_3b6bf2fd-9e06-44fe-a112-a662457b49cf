<?php
/**
 * Update Demographics Data
 * Handles AJAX requests to update visitor demographics
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

require_once '../config/config.php';
require_once '../config/database.php';
require_once '../classes/Analytics.php';

try {
    // Only accept POST requests
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Only POST requests allowed');
    }
    
    // Get JSON input
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        throw new Exception('Invalid JSON input');
    }
    
    // Validate input
    $age_range = $input['age_range'] ?? null;
    $gender = $input['gender'] ?? null;
    $consent = $input['consent'] ?? false;
    
    if (!$consent) {
        throw new Exception('Consent required');
    }
    
    // Validate age range
    $valid_age_ranges = ['under-18', '18-24', '25-34', '35-44', '45-54', '55-64', '65-plus'];
    if ($age_range && !in_array($age_range, $valid_age_ranges)) {
        throw new Exception('Invalid age range');
    }
    
    // Validate gender
    $valid_genders = ['male', 'female', 'non-binary', 'other'];
    if ($gender && !in_array($gender, $valid_genders)) {
        throw new Exception('Invalid gender');
    }
    
    // Get visitor hash from session or create one
    if (!session_id()) {
        session_start();
    }
    
    $visitor_ip = $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1';
    $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? '';
    $visitor_hash = hash('sha256', $visitor_ip . $user_agent . date('Y-m-d'));
    
    // Update demographics
    $analytics = new Analytics();
    $success = $analytics->updateDemographics($visitor_hash, $age_range, $gender, $consent);
    
    if ($success) {
        echo json_encode([
            'success' => true,
            'message' => 'Demographics updated successfully'
        ]);
    } else {
        throw new Exception('Failed to update demographics');
    }
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}
?>
