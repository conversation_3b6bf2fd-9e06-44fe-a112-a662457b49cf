<?php
require_once 'config/config.php';
require_once 'config/database.php';

echo "<h2>🔍 Analytics Debug Information</h2>";

try {
    $db = Database::getInstance();
    
    echo "<h3>1. Database Tables Check</h3>";
    
    // Check if analytics tables exist
    $tables = ['analytics_visits', 'analytics_artist_stats', 'analytics_traffic_sources', 'analytics_sessions', 'analytics_daily_stats'];
    
    foreach ($tables as $table) {
        try {
            $stmt = $db->query("SELECT COUNT(*) as count FROM $table");
            $count = $stmt->fetch()['count'];
            echo "<p style='color: green;'>✅ Table '$table' exists with $count records</p>";
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ Table '$table' error: " . $e->getMessage() . "</p>";
        }
    }
    
    echo "<h3>2. Recent Analytics Data</h3>";
    
    // Show recent visits
    try {
        $stmt = $db->query("SELECT * FROM analytics_visits ORDER BY visited_at DESC LIMIT 10");
        $visits = $stmt->fetchAll();
        
        if ($visits) {
            echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
            echo "<tr><th>ID</th><th>Artist ID</th><th>Visit Type</th><th>Visitor Hash</th><th>Device</th><th>Browser</th><th>Country</th><th>Visited At</th></tr>";
            foreach ($visits as $visit) {
                echo "<tr>";
                echo "<td>{$visit['id']}</td>";
                echo "<td>{$visit['artist_id']}</td>";
                echo "<td>{$visit['visit_type']}</td>";
                echo "<td>" . substr($visit['visitor_hash'], 0, 8) . "...</td>";
                echo "<td>{$visit['device_type']}</td>";
                echo "<td>{$visit['browser']}</td>";
                echo "<td>{$visit['country']}</td>";
                echo "<td>{$visit['visited_at']}</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p style='color: orange;'>⚠️ No visits recorded in analytics_visits table</p>";
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Error querying visits: " . $e->getMessage() . "</p>";
    }
    
    echo "<h3>3. Test Analytics Tracking</h3>";
    
    if (isset($_GET['test'])) {
        echo "<p>🧪 Testing analytics tracking...</p>";
        
        try {
            require_once 'classes/Analytics.php';
            $analytics = new Analytics();
            
            // Get a test artist
            $stmt = $db->query("SELECT id FROM artists LIMIT 1");
            $artist = $stmt->fetch();
            
            if ($artist) {
                $result = $analytics->trackVisit($artist['id'], 'test');
                if ($result) {
                    echo "<p style='color: green;'>✅ Analytics tracking test successful!</p>";
                } else {
                    echo "<p style='color: red;'>❌ Analytics tracking test failed</p>";
                }
            } else {
                echo "<p style='color: orange;'>⚠️ No artists found for testing</p>";
            }
            
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ Analytics tracking error: " . $e->getMessage() . "</p>";
        }
        
        // Refresh to show updated data
        echo "<script>setTimeout(() => window.location.href = 'debug_analytics.php', 2000);</script>";
    } else {
        echo "<p><a href='?test=1' style='background: #3273dc; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🧪 Test Analytics Tracking</a></p>";
    }
    
    echo "<h3>4. Analytics Dashboard Query Test</h3>";
    
    // Test the same queries used in analytics dashboard
    $date_from = date('Y-m-d', strtotime('-30 days'));
    $date_to = date('Y-m-d');
    
    echo "<p><strong>Date Range:</strong> $date_from to $date_to</p>";
    
    try {
        // Total views
        $stmt = $db->query("SELECT COUNT(*) as total_views FROM analytics_visits WHERE visited_at BETWEEN ? AND ?", 
                          [$date_from . ' 00:00:00', $date_to . ' 23:59:59']);
        $total_views = $stmt->fetch()['total_views'] ?? 0;
        echo "<p><strong>Total Views:</strong> $total_views</p>";
        
        // Unique visitors
        $stmt = $db->query("SELECT COUNT(DISTINCT visitor_hash) as unique_visitors FROM analytics_visits WHERE visited_at BETWEEN ? AND ?", 
                          [$date_from . ' 00:00:00', $date_to . ' 23:59:59']);
        $unique_visitors = $stmt->fetch()['unique_visitors'] ?? 0;
        echo "<p><strong>Unique Visitors:</strong> $unique_visitors</p>";
        
        // Popular artists
        $stmt = $db->query("SELECT a.name, a.slug, COUNT(av.id) as views 
                           FROM analytics_visits av 
                           JOIN artists a ON av.artist_id = a.id 
                           WHERE av.visited_at BETWEEN ? AND ? AND av.artist_id IS NOT NULL
                           GROUP BY av.artist_id 
                           ORDER BY views DESC LIMIT 5", 
                          [$date_from . ' 00:00:00', $date_to . ' 23:59:59']);
        $popular_artists = $stmt->fetchAll();
        
        echo "<p><strong>Popular Artists:</strong></p>";
        if ($popular_artists) {
            echo "<ul>";
            foreach ($popular_artists as $artist) {
                echo "<li>{$artist['name']} ({$artist['slug']}) - {$artist['views']} views</li>";
            }
            echo "</ul>";
        } else {
            echo "<p>No artist visits found</p>";
        }
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Dashboard query error: " . $e->getMessage() . "</p>";
    }
    
    echo "<h3>5. Session Information</h3>";
    echo "<p><strong>Current Session ID:</strong> " . (session_id() ?: 'No session') . "</p>";
    echo "<p><strong>User Agent:</strong> " . ($_SERVER['HTTP_USER_AGENT'] ?? 'Not set') . "</p>";
    echo "<p><strong>IP Address:</strong> " . ($_SERVER['REMOTE_ADDR'] ?? 'Not set') . "</p>";
    echo "<p><strong>Referrer:</strong> " . ($_SERVER['HTTP_REFERER'] ?? 'Direct') . "</p>";
    
    echo "<h3>6. Navigation</h3>";
    echo "<p><a href='admin/analytics.php'>📊 Analytics Dashboard</a></p>";
    echo "<p><a href='admin/index.php'>🏠 Admin Dashboard</a></p>";
    
    // Show raw visitor hash calculation
    if (class_exists('Analytics')) {
        $ip = $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1';
        $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? '';
        $hash = hash('sha256', $ip . $user_agent . date('Y-m-d'));
        echo "<p><strong>Current Visitor Hash:</strong> " . substr($hash, 0, 16) . "...</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Database connection error: " . htmlspecialchars($e->getMessage()) . "</p>";
}
?>
