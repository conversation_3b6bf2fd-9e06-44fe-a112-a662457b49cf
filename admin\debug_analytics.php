<?php
/**
 * Debug Analytics Data
 * Check what data is available in advanced analytics tables
 */

require_once '../config/config.php';
require_once '../config/database.php';
require_once 'includes/auth_check.php';

$db = Database::getInstance();

// Get date range
$date_from = $_GET['from'] ?? date('Y-m-d', strtotime('-30 days'));
$date_to = $_GET['to'] ?? date('Y-m-d');

echo "<h1>Analytics Debug Information</h1>";
echo "<p>Date Range: {$date_from} to {$date_to}</p>";

try {
    // Check User Journey Data
    echo "<h2>User Journey Data</h2>";
    $stmt = $db->query("SELECT 
                           COUNT(*) as total_sessions,
                           AVG(time_spent) as avg_time_spent,
                           AVG(scroll_depth) as avg_scroll_depth,
                           AVG(interactions_count) as avg_interactions
                       FROM analytics_user_journeys
                       WHERE entry_time BETWEEN ? AND ?",
                      [$date_from . ' 00:00:00', $date_to . ' 23:59:59']);
    $journey_stats = $stmt->fetch();
    echo "<pre>" . print_r($journey_stats, true) . "</pre>";
    
    // Check Gallery Interactions
    echo "<h2>Gallery Interactions</h2>";
    $stmt = $db->query("SELECT interaction_type, COUNT(*) as count
                       FROM analytics_gallery_interactions
                       WHERE timestamp BETWEEN ? AND ?
                       GROUP BY interaction_type
                       ORDER BY count DESC",
                      [$date_from . ' 00:00:00', $date_to . ' 23:59:59']);
    $gallery_interactions = $stmt->fetchAll();
    echo "<pre>" . print_r($gallery_interactions, true) . "</pre>";
    
    // Check Social Clicks
    echo "<h2>Social Clicks</h2>";
    $stmt = $db->query("SELECT social_platform, COUNT(*) as clicks
                       FROM analytics_social_clicks
                       WHERE timestamp BETWEEN ? AND ?
                       GROUP BY social_platform
                       ORDER BY clicks DESC",
                      [$date_from . ' 00:00:00', $date_to . ' 23:59:59']);
    $social_clicks = $stmt->fetchAll();
    echo "<pre>" . print_r($social_clicks, true) . "</pre>";
    
    // Check Conversion Funnels
    echo "<h2>Conversion Funnels</h2>";
    $stmt = $db->query("SELECT funnel_step, COUNT(*) as count
                       FROM analytics_conversion_funnels
                       WHERE timestamp BETWEEN ? AND ?
                       GROUP BY funnel_step
                       ORDER BY FIELD(funnel_step, 'landing', 'profile_view', 'gallery_view', 'social_click', 'contact_attempt')",
                      [$date_from . ' 00:00:00', $date_to . ' 23:59:59']);
    $conversion_funnels = $stmt->fetchAll();
    echo "<pre>" . print_r($conversion_funnels, true) . "</pre>";
    
    // Check Recent Raw Data
    echo "<h2>Recent Gallery Interactions (Raw)</h2>";
    $stmt = $db->query("SELECT * FROM analytics_gallery_interactions ORDER BY timestamp DESC LIMIT 10");
    $recent_interactions = $stmt->fetchAll();
    echo "<pre>" . print_r($recent_interactions, true) . "</pre>";
    
    echo "<h2>Recent User Journeys (Raw)</h2>";
    $stmt = $db->query("SELECT * FROM analytics_user_journeys ORDER BY entry_time DESC LIMIT 5");
    $recent_journeys = $stmt->fetchAll();
    echo "<pre>" . print_r($recent_journeys, true) . "</pre>";
    
    echo "<h2>Recent Conversion Funnels (Raw)</h2>";
    $stmt = $db->query("SELECT * FROM analytics_conversion_funnels ORDER BY timestamp DESC LIMIT 10");
    $recent_conversions = $stmt->fetchAll();
    echo "<pre>" . print_r($recent_conversions, true) . "</pre>";
    
} catch (Exception $e) {
    echo "<h2>Error:</h2>";
    echo "<p style='color: red;'>" . $e->getMessage() . "</p>";
}

echo "<p><a href='analytics.php'>Back to Analytics Dashboard</a></p>";
?>
