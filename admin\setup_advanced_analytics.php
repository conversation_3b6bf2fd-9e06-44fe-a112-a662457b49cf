<?php
/**
 * Setup Advanced Analytics Tables
 * Creates tables for user journey tracking, gallery interactions, social clicks, and conversion funnels
 */

require_once '../config/config.php';
require_once '../config/database.php';

try {
    $db = Database::getInstance();
    
    echo "<h2>Setting up Advanced Analytics Tables...</h2>";
    
    // 1. User Journey Tracking Table
    $sql = "CREATE TABLE IF NOT EXISTS analytics_gallery_interactions (
        id INT AUTO_INCREMENT PRIMARY KEY,
        session_id VARCHAR(64) NOT NULL,
        visitor_hash VARCHAR(64) NOT NULL,
        artist_id INT NOT NULL,
        gallery_image_id INT NULL,
        interaction_type ENUM('view', 'click', 'modal_open', 'modal_close', 'navigation', 'zoom') NOT NULL,
        interaction_data JSON NULL,
        x_coordinate INT NULL,
        y_coordinate INT NULL,
        timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_session (session_id),
        INDEX idx_artist (artist_id),
        INDEX idx_type (interaction_type),
        INDEX idx_timestamp (timestamp)
    )";
    
    $db->query($sql);
    echo "✅ Gallery interactions table created<br>";
    
    // 2. Social Media Click Tracking Table
    $sql = "CREATE TABLE IF NOT EXISTS analytics_social_clicks (
        id INT AUTO_INCREMENT PRIMARY KEY,
        session_id VARCHAR(64) NOT NULL,
        visitor_hash VARCHAR(64) NOT NULL,
        artist_id INT NOT NULL,
        social_platform VARCHAR(50) NOT NULL,
        click_url VARCHAR(500) NOT NULL,
        click_position VARCHAR(50),
        timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_session (session_id),
        INDEX idx_artist (artist_id),
        INDEX idx_platform (social_platform),
        INDEX idx_timestamp (timestamp)
    )";
    
    $db->query($sql);
    echo "✅ Social clicks table created<br>";
    
    // 3. Conversion Funnel Tracking Table
    $sql = "CREATE TABLE IF NOT EXISTS analytics_conversion_funnels (
        id INT AUTO_INCREMENT PRIMARY KEY,
        session_id VARCHAR(64) NOT NULL,
        visitor_hash VARCHAR(64) NOT NULL,
        artist_id INT NULL,
        funnel_step ENUM('landing', 'profile_view', 'gallery_view', 'social_click', 'contact_attempt') NOT NULL,
        step_data JSON NULL,
        timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_session (session_id),
        INDEX idx_artist (artist_id),
        INDEX idx_step (funnel_step),
        INDEX idx_timestamp (timestamp)
    )";
    
    $db->query($sql);
    echo "✅ Conversion funnels table created<br>";
    
    // 4. Page Performance Tracking Table
    $sql = "CREATE TABLE IF NOT EXISTS analytics_page_performance (
        id INT AUTO_INCREMENT PRIMARY KEY,
        session_id VARCHAR(64) NOT NULL,
        visitor_hash VARCHAR(64) NOT NULL,
        page_url VARCHAR(500) NOT NULL,
        artist_id INT NULL,
        load_time INT NOT NULL,
        time_to_first_byte INT NULL,
        dom_content_loaded INT NULL,
        first_paint INT NULL,
        largest_contentful_paint INT NULL,
        cumulative_layout_shift DECIMAL(5,3) NULL,
        timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_session (session_id),
        INDEX idx_artist (artist_id),
        INDEX idx_timestamp (timestamp)
    )";
    
    $db->query($sql);
    echo "✅ Page performance table created<br>";
    
    // 5. Heatmap Data Table
    $sql = "CREATE TABLE IF NOT EXISTS analytics_heatmap_data (
        id INT AUTO_INCREMENT PRIMARY KEY,
        session_id VARCHAR(64) NOT NULL,
        visitor_hash VARCHAR(64) NOT NULL,
        page_url VARCHAR(500) NOT NULL,
        artist_id INT NULL,
        element_selector VARCHAR(200),
        interaction_type ENUM('click', 'hover', 'scroll', 'focus') NOT NULL,
        x_coordinate INT NOT NULL,
        y_coordinate INT NOT NULL,
        viewport_width INT NOT NULL,
        viewport_height INT NOT NULL,
        timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_session (session_id),
        INDEX idx_artist (artist_id),
        INDEX idx_page (page_url),
        INDEX idx_timestamp (timestamp)
    )";
    
    $db->query($sql);
    echo "✅ Heatmap data table created<br>";
    
    echo "<h3>✅ All Advanced Analytics Tables Created Successfully!</h3>";
    echo "<p><a href='analytics.php'>Go to Analytics Dashboard</a></p>";
    
} catch (Exception $e) {
    echo "<h3>❌ Error: " . $e->getMessage() . "</h3>";
}
?>
